// Arabic PDF Export Support
class ArabicPDFExporter {
    constructor() {
        this.arabicFont = null;
        this.isRTL = false;
        this.init();
    }

    async init() {
        // Load Arabic font for PDF
        try {
            // You would need to include an Arabic font file
            // For now, we'll use a fallback approach
            this.arabicFont = 'Arial'; // Fallback font
            console.log('Arabic PDF exporter initialized');
        } catch (error) {
            console.error('Failed to load Arabic font:', error);
        }
    }

    async exportToPDF(cvData, templateType = 'modern') {
        try {
            // Check if we're in RTL mode
            this.isRTL = document.documentElement.dir === 'rtl';
            
            // Create new jsPDF instance with RTL support
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4'
            });

            // Set up Arabic text support
            this.setupArabicSupport(doc);

            // Generate PDF content based on template
            switch (templateType) {
                case 'modern':
                    await this.generateModernTemplate(doc, cvData);
                    break;
                case 'classic':
                    await this.generateClassicTemplate(doc, cvData);
                    break;
                case 'creative':
                    await this.generateCreativeTemplate(doc, cvData);
                    break;
                default:
                    await this.generateModernTemplate(doc, cvData);
            }

            // Save the PDF
            const fileName = this.isRTL ? 
                `${cvData.personal.fullName || 'السيرة_الذاتية'}_CV.pdf` :
                `${cvData.personal.fullName || 'CV'}_Resume.pdf`;
            
            doc.save(fileName);
            
            return { success: true, message: this.isRTL ? 'تم تصدير PDF بنجاح' : 'PDF exported successfully' };
        } catch (error) {
            console.error('PDF export error:', error);
            return { 
                success: false, 
                message: this.isRTL ? 'فشل في تصدير PDF' : 'Failed to export PDF',
                error 
            };
        }
    }

    setupArabicSupport(doc) {
        // Set default font for Arabic text
        doc.setFont('Arial', 'normal');
        
        // Set text direction for RTL
        if (this.isRTL) {
            // Note: jsPDF doesn't have native RTL support
            // We'll need to handle text positioning manually
            doc.setR2L(true);
        }
    }

    async generateModernTemplate(doc, cvData) {
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const margin = 20;
        let yPosition = margin;

        // Header Section
        yPosition = this.addHeader(doc, cvData, yPosition, pageWidth, margin);
        
        // Professional Summary
        if (cvData.summary) {
            yPosition = this.addSection(doc, 
                this.isRTL ? 'الملخص المهني' : 'Professional Summary', 
                cvData.summary, 
                yPosition, 
                pageWidth, 
                margin
            );
        }

        // Work Experience
        if (cvData.experience && cvData.experience.length > 0) {
            yPosition = this.addExperienceSection(doc, cvData.experience, yPosition, pageWidth, margin);
        }

        // Education
        if (cvData.education && cvData.education.length > 0) {
            yPosition = this.addEducationSection(doc, cvData.education, yPosition, pageWidth, margin);
        }

        // Skills
        if (cvData.skills && cvData.skills.length > 0) {
            yPosition = this.addSkillsSection(doc, cvData.skills, yPosition, pageWidth, margin);
        }

        // Projects
        if (cvData.projects && cvData.projects.length > 0) {
            yPosition = this.addProjectsSection(doc, cvData.projects, yPosition, pageWidth, margin);
        }

        // Languages
        if (cvData.languages && cvData.languages.length > 0) {
            yPosition = this.addLanguagesSection(doc, cvData.languages, yPosition, pageWidth, margin);
        }
    }

    addHeader(doc, cvData, yPosition, pageWidth, margin) {
        const personal = cvData.personal;
        
        // Name
        doc.setFontSize(24);
        doc.setFont('Arial', 'bold');
        const nameText = personal.fullName || (this.isRTL ? 'الاسم غير محدد' : 'Name Not Specified');
        const nameWidth = doc.getTextWidth(nameText);
        const nameX = this.isRTL ? pageWidth - margin - nameWidth : margin;
        doc.text(nameText, nameX, yPosition);
        yPosition += 10;

        // Job Title
        if (personal.jobTitle) {
            doc.setFontSize(16);
            doc.setFont('Arial', 'normal');
            doc.setTextColor(60, 60, 60);
            const titleWidth = doc.getTextWidth(personal.jobTitle);
            const titleX = this.isRTL ? pageWidth - margin - titleWidth : margin;
            doc.text(personal.jobTitle, titleX, yPosition);
            yPosition += 8;
        }

        // Contact Information
        doc.setFontSize(10);
        doc.setTextColor(80, 80, 80);
        const contactInfo = [];
        
        if (personal.email) contactInfo.push(personal.email);
        if (personal.phone) contactInfo.push(personal.phone);
        if (personal.location) contactInfo.push(personal.location);
        
        if (contactInfo.length > 0) {
            const contactText = contactInfo.join(' | ');
            const contactWidth = doc.getTextWidth(contactText);
            const contactX = this.isRTL ? pageWidth - margin - contactWidth : margin;
            doc.text(contactText, contactX, yPosition);
            yPosition += 6;
        }

        // Add separator line
        doc.setDrawColor(200, 200, 200);
        doc.line(margin, yPosition, pageWidth - margin, yPosition);
        yPosition += 10;

        doc.setTextColor(0, 0, 0); // Reset color
        return yPosition;
    }

    addSection(doc, title, content, yPosition, pageWidth, margin) {
        // Section title
        doc.setFontSize(14);
        doc.setFont('Arial', 'bold');
        doc.setTextColor(40, 40, 40);
        
        const titleWidth = doc.getTextWidth(title);
        const titleX = this.isRTL ? pageWidth - margin - titleWidth : margin;
        doc.text(title, titleX, yPosition);
        yPosition += 8;

        // Section content
        doc.setFontSize(10);
        doc.setFont('Arial', 'normal');
        doc.setTextColor(60, 60, 60);
        
        // Handle text wrapping for Arabic/RTL
        const maxWidth = pageWidth - (2 * margin);
        const lines = this.wrapText(doc, content, maxWidth);
        
        lines.forEach(line => {
            const lineWidth = doc.getTextWidth(line);
            const lineX = this.isRTL ? pageWidth - margin - lineWidth : margin;
            doc.text(line, lineX, yPosition);
            yPosition += 5;
        });

        yPosition += 5; // Extra spacing after section
        doc.setTextColor(0, 0, 0); // Reset color
        return yPosition;
    }

    addExperienceSection(doc, experiences, yPosition, pageWidth, margin) {
        const title = this.isRTL ? 'الخبرة العملية' : 'Work Experience';
        yPosition = this.addSectionTitle(doc, title, yPosition, pageWidth, margin);

        experiences.forEach(exp => {
            // Job title and company
            doc.setFontSize(12);
            doc.setFont('Arial', 'bold');
            const jobText = `${exp.title} - ${exp.company}`;
            const jobWidth = doc.getTextWidth(jobText);
            const jobX = this.isRTL ? pageWidth - margin - jobWidth : margin;
            doc.text(jobText, jobX, yPosition);
            yPosition += 6;

            // Date and location
            doc.setFontSize(10);
            doc.setFont('Arial', 'normal');
            doc.setTextColor(80, 80, 80);
            const dateText = `${exp.startDate} - ${exp.endDate || (this.isRTL ? 'حتى الآن' : 'Present')}`;
            if (exp.location) {
                const locationText = ` | ${exp.location}`;
                const fullDateText = dateText + locationText;
                const dateWidth = doc.getTextWidth(fullDateText);
                const dateX = this.isRTL ? pageWidth - margin - dateWidth : margin;
                doc.text(fullDateText, dateX, yPosition);
            } else {
                const dateWidth = doc.getTextWidth(dateText);
                const dateX = this.isRTL ? pageWidth - margin - dateWidth : margin;
                doc.text(dateText, dateX, yPosition);
            }
            yPosition += 5;

            // Description
            if (exp.description) {
                doc.setTextColor(60, 60, 60);
                const maxWidth = pageWidth - (2 * margin);
                const lines = this.wrapText(doc, exp.description, maxWidth);
                
                lines.forEach(line => {
                    const lineWidth = doc.getTextWidth(line);
                    const lineX = this.isRTL ? pageWidth - margin - lineWidth : margin;
                    doc.text(line, lineX, yPosition);
                    yPosition += 4;
                });
            }

            yPosition += 5; // Space between experiences
            doc.setTextColor(0, 0, 0); // Reset color
        });

        return yPosition + 5;
    }

    addEducationSection(doc, education, yPosition, pageWidth, margin) {
        const title = this.isRTL ? 'التعليم' : 'Education';
        yPosition = this.addSectionTitle(doc, title, yPosition, pageWidth, margin);

        education.forEach(edu => {
            // Degree and school
            doc.setFontSize(12);
            doc.setFont('Arial', 'bold');
            const eduText = `${edu.degree} - ${edu.school}`;
            const eduWidth = doc.getTextWidth(eduText);
            const eduX = this.isRTL ? pageWidth - margin - eduWidth : margin;
            doc.text(eduText, eduX, yPosition);
            yPosition += 6;

            // Date
            doc.setFontSize(10);
            doc.setFont('Arial', 'normal');
            doc.setTextColor(80, 80, 80);
            const dateText = `${edu.startDate} - ${edu.endDate}`;
            const dateWidth = doc.getTextWidth(dateText);
            const dateX = this.isRTL ? pageWidth - margin - dateWidth : margin;
            doc.text(dateText, dateX, yPosition);
            yPosition += 8;

            doc.setTextColor(0, 0, 0); // Reset color
        });

        return yPosition + 5;
    }

    addSkillsSection(doc, skills, yPosition, pageWidth, margin) {
        const title = this.isRTL ? 'المهارات' : 'Skills';
        yPosition = this.addSectionTitle(doc, title, yPosition, pageWidth, margin);

        doc.setFontSize(10);
        doc.setFont('Arial', 'normal');
        
        const skillsText = skills.map(skill => `${skill.name} (${skill.level})`).join(' • ');
        const maxWidth = pageWidth - (2 * margin);
        const lines = this.wrapText(doc, skillsText, maxWidth);
        
        lines.forEach(line => {
            const lineWidth = doc.getTextWidth(line);
            const lineX = this.isRTL ? pageWidth - margin - lineWidth : margin;
            doc.text(line, lineX, yPosition);
            yPosition += 5;
        });

        return yPosition + 5;
    }

    addProjectsSection(doc, projects, yPosition, pageWidth, margin) {
        const title = this.isRTL ? 'المشاريع' : 'Projects';
        yPosition = this.addSectionTitle(doc, title, yPosition, pageWidth, margin);

        projects.forEach(project => {
            doc.setFontSize(12);
            doc.setFont('Arial', 'bold');
            const projectWidth = doc.getTextWidth(project.name);
            const projectX = this.isRTL ? pageWidth - margin - projectWidth : margin;
            doc.text(project.name, projectX, yPosition);
            yPosition += 6;

            if (project.description) {
                doc.setFontSize(10);
                doc.setFont('Arial', 'normal');
                doc.setTextColor(60, 60, 60);
                const maxWidth = pageWidth - (2 * margin);
                const lines = this.wrapText(doc, project.description, maxWidth);
                
                lines.forEach(line => {
                    const lineWidth = doc.getTextWidth(line);
                    const lineX = this.isRTL ? pageWidth - margin - lineWidth : margin;
                    doc.text(line, lineX, yPosition);
                    yPosition += 4;
                });
            }

            yPosition += 5;
            doc.setTextColor(0, 0, 0);
        });

        return yPosition + 5;
    }

    addLanguagesSection(doc, languages, yPosition, pageWidth, margin) {
        const title = this.isRTL ? 'اللغات' : 'Languages';
        yPosition = this.addSectionTitle(doc, title, yPosition, pageWidth, margin);

        doc.setFontSize(10);
        doc.setFont('Arial', 'normal');
        
        const languagesText = languages.map(lang => `${lang.name} (${lang.proficiency})`).join(' • ');
        const maxWidth = pageWidth - (2 * margin);
        const lines = this.wrapText(doc, languagesText, maxWidth);
        
        lines.forEach(line => {
            const lineWidth = doc.getTextWidth(line);
            const lineX = this.isRTL ? pageWidth - margin - lineWidth : margin;
            doc.text(line, lineX, yPosition);
            yPosition += 5;
        });

        return yPosition + 5;
    }

    addSectionTitle(doc, title, yPosition, pageWidth, margin) {
        doc.setFontSize(14);
        doc.setFont('Arial', 'bold');
        doc.setTextColor(40, 40, 40);
        
        const titleWidth = doc.getTextWidth(title);
        const titleX = this.isRTL ? pageWidth - margin - titleWidth : margin;
        doc.text(title, titleX, yPosition);
        
        doc.setTextColor(0, 0, 0);
        return yPosition + 8;
    }

    wrapText(doc, text, maxWidth) {
        const words = text.split(' ');
        const lines = [];
        let currentLine = '';

        words.forEach(word => {
            const testLine = currentLine + (currentLine ? ' ' : '') + word;
            const testWidth = doc.getTextWidth(testLine);
            
            if (testWidth > maxWidth && currentLine) {
                lines.push(currentLine);
                currentLine = word;
            } else {
                currentLine = testLine;
            }
        });

        if (currentLine) {
            lines.push(currentLine);
        }

        return lines;
    }

    async generateClassicTemplate(doc, cvData) {
        // Similar to modern but with different styling
        await this.generateModernTemplate(doc, cvData);
    }

    async generateCreativeTemplate(doc, cvData) {
        // Similar to modern but with different styling
        await this.generateModernTemplate(doc, cvData);
    }
}

// Create global instance
window.arabicPDFExporter = new ArabicPDFExporter();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ArabicPDFExporter;
}
