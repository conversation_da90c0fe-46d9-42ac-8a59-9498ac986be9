<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول البسيط</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            direction: rtl;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <h1>🔐 اختبار تسجيل الدخول البسيط</h1>
    
    <div id="status" class="result info">جاري التحميل...</div>

    <h3>إنشاء حساب جديد</h3>
    <form id="signup-form">
        <div class="form-group">
            <label>الاسم الكامل:</label>
            <input type="text" id="signup-name" value="مستخدم تجريبي" required>
        </div>
        <div class="form-group">
            <label>البريد الإلكتروني:</label>
            <input type="email" id="signup-email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label>كلمة المرور:</label>
            <input type="password" id="signup-password" value="123456" required>
        </div>
        <button type="submit">إنشاء حساب</button>
    </form>

    <h3>تسجيل الدخول</h3>
    <form id="login-form">
        <div class="form-group">
            <label>البريد الإلكتروني:</label>
            <input type="email" id="login-email" value="<EMAIL>" required>
        </div>
        <div class="form-group">
            <label>كلمة المرور:</label>
            <input type="password" id="login-password" value="123456" required>
        </div>
        <button type="submit">تسجيل الدخول</button>
    </form>

    <div id="result"></div>

    <h3>معلومات التشخيص</h3>
    <button onclick="showDiagnostics()">عرض معلومات التشخيص</button>
    <div id="diagnostics"></div>

    <script>
        // إعدادات Supabase
        const SUPABASE_URL = 'https://hoyzvlfeyzzqbmhmsypy.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhveXp2bGZleXp6cWJtaG1zeXB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDk4NDQsImV4cCI6MjA2NTA4NTg0NH0.0PTd0lfyPxXQmU1akMISJYU3eAcLPCZzrJxXrKnus0U';

        let supabase = null;

        // تهيئة Supabase
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                if (window.supabase) {
                    supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
                    document.getElementById('status').innerHTML = '<div class="success">✅ تم تحميل Supabase بنجاح</div>';
                    
                    // اختبار الاتصال
                    await testConnection();
                } else {
                    document.getElementById('status').innerHTML = '<div class="error">❌ فشل في تحميل Supabase</div>';
                }
            } catch (error) {
                document.getElementById('status').innerHTML = `<div class="error">❌ خطأ في التهيئة: ${error.message}</div>`;
            }

            // إعداد معالجات النماذج
            document.getElementById('signup-form').addEventListener('submit', handleSignup);
            document.getElementById('login-form').addEventListener('submit', handleLogin);
        });

        async function testConnection() {
            try {
                const { data, error } = await supabase
                    .from('cv_templates')
                    .select('id')
                    .limit(1);

                if (error) {
                    document.getElementById('status').innerHTML += `<div class="error">❌ فشل اختبار قاعدة البيانات: ${error.message}</div>`;
                } else {
                    document.getElementById('status').innerHTML += '<div class="success">✅ قاعدة البيانات متصلة</div>';
                }
            } catch (error) {
                document.getElementById('status').innerHTML += `<div class="error">❌ خطأ في اختبار الاتصال: ${error.message}</div>`;
            }
        }

        async function handleSignup(event) {
            event.preventDefault();
            
            const name = document.getElementById('signup-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">جاري إنشاء الحساب...</div>';

            try {
                console.log('محاولة إنشاء حساب:', { name, email });

                const { data, error } = await supabase.auth.signUp({
                    email: email.toLowerCase().trim(),
                    password,
                    options: {
                        data: {
                            full_name: name.trim()
                        }
                    }
                });

                console.log('نتيجة إنشاء الحساب:', { data, error });

                if (error) {
                    resultDiv.innerHTML = `<div class="error">❌ فشل إنشاء الحساب: ${error.message}</div>`;
                } else {
                    resultDiv.innerHTML = `
                        <div class="success">✅ تم إنشاء الحساب بنجاح!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <p><strong>ملاحظة:</strong> تحقق من بريدك الإلكتروني لتفعيل الحساب</p>
                    `;
                }
            } catch (error) {
                console.error('خطأ في إنشاء الحساب:', error);
                resultDiv.innerHTML = `<div class="error">❌ خطأ غير متوقع: ${error.message}</div>`;
            }
        }

        async function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">جاري تسجيل الدخول...</div>';

            try {
                console.log('محاولة تسجيل الدخول:', { email });

                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email.toLowerCase().trim(),
                    password
                });

                console.log('نتيجة تسجيل الدخول:', { data, error });

                if (error) {
                    let errorMessage = error.message;
                    
                    // ترجمة رسائل الخطأ الشائعة
                    if (errorMessage.includes('Invalid login credentials')) {
                        errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                    } else if (errorMessage.includes('Email not confirmed')) {
                        errorMessage = 'يرجى تأكيد بريدك الإلكتروني أولاً';
                    }
                    
                    resultDiv.innerHTML = `<div class="error">❌ فشل تسجيل الدخول: ${errorMessage}</div>`;
                } else {
                    resultDiv.innerHTML = `
                        <div class="success">✅ تم تسجيل الدخول بنجاح!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                resultDiv.innerHTML = `<div class="error">❌ خطأ غير متوقع: ${error.message}</div>`;
            }
        }

        function showDiagnostics() {
            const diagnostics = {
                'Supabase متوفر': !!window.supabase,
                'Supabase Client متوفر': !!supabase,
                'URL': SUPABASE_URL,
                'Key': SUPABASE_ANON_KEY ? '[موجود]' : '[غير موجود]',
                'User Agent': navigator.userAgent,
                'الوقت': new Date().toISOString()
            };

            document.getElementById('diagnostics').innerHTML = `<pre>${JSON.stringify(diagnostics, null, 2)}</pre>`;
        }

        // مراقبة تغييرات حالة المصادقة
        if (window.supabase) {
            window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY).auth.onAuthStateChange((event, session) => {
                console.log('تغيير حالة المصادقة:', event, session);
                
                if (event === 'SIGNED_IN') {
                    document.getElementById('status').innerHTML += '<div class="success">✅ تم تسجيل الدخول بنجاح!</div>';
                } else if (event === 'SIGNED_OUT') {
                    document.getElementById('status').innerHTML += '<div class="info">ℹ️ تم تسجيل الخروج</div>';
                }
            });
        }
    </script>
</body>
</html>
