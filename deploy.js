#!/usr/bin/env node

// Automated deployment script for Elashrafy CV
const https = require('https');

const SUPABASE_URL = 'https://api.supabase.com/v1/projects/hoyzvlfeyzzqbmhmsypy/database/query';
const SERVICE_ROLE_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhveXp2bGZleXp6cWJtaG1zeXB5Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTUwOTg0NCwiZXhwIjoyMDY1MDg1ODQ0fQ.gQvRvFWYOUn-Zejs20AYZu9w0h-p869NlynUszWyJ_4';

const setupSQL = `
-- Production Database Setup for Elashrafy CV
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT NOT NULL,
    avatar_url TEXT,
    phone TEXT,
    location TEXT,
    bio TEXT,
    website TEXT,
    linkedin TEXT,
    github TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create CV templates table
CREATE TABLE IF NOT EXISTS public.cv_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    category TEXT NOT NULL DEFAULT 'modern',
    template_data JSONB NOT NULL DEFAULT '{}',
    preview_image TEXT,
    is_active BOOLEAN DEFAULT true,
    is_premium BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cv_templates ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
CREATE POLICY "Users can view own profile" ON public.profiles 
    FOR SELECT USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
CREATE POLICY "Users can update own profile" ON public.profiles 
    FOR UPDATE USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;
CREATE POLICY "Users can insert own profile" ON public.profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Create policy for templates (public read)
DROP POLICY IF EXISTS "Templates are public" ON public.cv_templates;
CREATE POLICY "Templates are public" ON public.cv_templates 
    FOR SELECT USING (is_active = true);

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for automatic profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert default templates
INSERT INTO public.cv_templates (name, name_ar, description, description_ar, category, template_data, is_active) VALUES
(
    'Modern Professional',
    'مهني حديث',
    'Clean and modern design perfect for tech professionals',
    'تصميم نظيف وحديث مثالي للمهنيين التقنيين',
    'modern',
    '{"colors": {"primary": "#2563eb", "secondary": "#64748b"}, "fonts": {"primary": "Cairo", "secondary": "Inter"}}',
    true
),
(
    'Classic Executive',
    'تنفيذي كلاسيكي',
    'Traditional layout ideal for executive positions',
    'تخطيط تقليدي مثالي للمناصب التنفيذية',
    'classic',
    '{"colors": {"primary": "#f59e0b", "secondary": "#6b7280"}, "fonts": {"primary": "Noto Sans Arabic", "secondary": "Georgia"}}',
    true
),
(
    'Creative Designer',
    'مصمم إبداعي',
    'Bold and creative design for creative professionals',
    'تصميم جريء وإبداعي للمهنيين المبدعين',
    'creative',
    '{"colors": {"primary": "#10b981", "secondary": "#8b5cf6"}, "fonts": {"primary": "Cairo", "secondary": "Poppins"}}',
    true
)
ON CONFLICT DO NOTHING;

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
`;

async function setupDatabase() {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify({
            query: setupSQL
        });

        const options = {
            hostname: 'api.supabase.com',
            port: 443,
            path: '/v1/projects/hoyzvlfeyzzqbmhmsypy/database/query',
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
                'Content-Type': 'application/json',
                'Content-Length': data.length
            }
        };

        const req = https.request(options, (res) => {
            let responseData = '';

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log('✅ Database setup completed successfully');
                    resolve(responseData);
                } else {
                    console.error('❌ Database setup failed:', res.statusCode, responseData);
                    reject(new Error(`HTTP ${res.statusCode}: ${responseData}`));
                }
            });
        });

        req.on('error', (error) => {
            console.error('❌ Request failed:', error);
            reject(error);
        });

        req.write(data);
        req.end();
    });
}

async function disableEmailConfirmation() {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify({
            enable_confirmations: false
        });

        const options = {
            hostname: 'api.supabase.com',
            port: 443,
            path: '/v1/projects/hoyzvlfeyzzqbmhmsypy/config/auth',
            method: 'PATCH',
            headers: {
                'Authorization': `Bearer ${SERVICE_ROLE_KEY}`,
                'Content-Type': 'application/json',
                'Content-Length': data.length
            }
        };

        const req = https.request(options, (res) => {
            let responseData = '';

            res.on('data', (chunk) => {
                responseData += chunk;
            });

            res.on('end', () => {
                if (res.statusCode === 200) {
                    console.log('✅ Email confirmation disabled');
                    resolve(responseData);
                } else {
                    console.log('⚠️ Could not disable email confirmation (may need manual setup)');
                    resolve(responseData); // Don't fail deployment for this
                }
            });
        });

        req.on('error', (error) => {
            console.log('⚠️ Email confirmation setup failed (continuing anyway)');
            resolve(); // Don't fail deployment for this
        });

        req.write(data);
        req.end();
    });
}

async function main() {
    console.log('🚀 Starting automated deployment setup...');
    
    try {
        console.log('📊 Setting up database...');
        await setupDatabase();
        
        console.log('📧 Configuring authentication...');
        await disableEmailConfirmation();
        
        console.log('✅ Deployment setup completed successfully!');
        console.log('🌐 Application is ready for deployment');
        console.log('📱 Test the application at: https://elashrafy-cv.netlify.app');
        
    } catch (error) {
        console.error('❌ Deployment setup failed:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { setupDatabase, disableEmailConfirmation };
