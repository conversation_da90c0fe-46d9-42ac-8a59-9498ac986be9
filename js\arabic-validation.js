// Arabic Form Validation and Input Handling
class ArabicFormValidator {
    constructor() {
        this.isRTL = document.documentElement.dir === 'rtl';
        this.currentLanguage = window.translationManager?.getCurrentLanguage() || 'ar';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupArabicInputHandling();
    }

    setupEventListeners() {
        // Listen for language changes
        window.addEventListener('languageChanged', (e) => {
            this.currentLanguage = e.detail.language;
            this.isRTL = this.currentLanguage === 'ar';
            this.updateFormDirection();
        });
    }

    setupArabicInputHandling() {
        // Handle Arabic text input
        document.addEventListener('input', (e) => {
            if (e.target.matches('input[type="text"], textarea')) {
                this.handleArabicInput(e.target);
            }
        });

        // Handle form submission
        document.addEventListener('submit', (e) => {
            if (e.target.matches('form')) {
                this.validateForm(e.target, e);
            }
        });
    }

    handleArabicInput(input) {
        const value = input.value;
        
        // Detect if input contains Arabic characters
        const hasArabic = this.containsArabic(value);
        
        if (hasArabic) {
            // Set RTL direction for Arabic text
            input.style.direction = 'rtl';
            input.style.textAlign = 'right';
        } else if (this.isRTL) {
            // Keep RTL for RTL layout even with English text
            input.style.direction = 'rtl';
            input.style.textAlign = 'right';
        } else {
            // LTR for English text in LTR layout
            input.style.direction = 'ltr';
            input.style.textAlign = 'left';
        }

        // Real-time validation
        this.validateField(input);
    }

    containsArabic(text) {
        const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
        return arabicRegex.test(text);
    }

    validateField(field) {
        const value = field.value.trim();
        const fieldName = field.getAttribute('data-field-name') || field.name || field.id;
        let isValid = true;
        let errorMessage = '';

        // Remove existing error styling
        this.clearFieldError(field);

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = this.getErrorMessage('required', fieldName);
        }

        // Email validation
        if (field.type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                isValid = false;
                errorMessage = this.getErrorMessage('email');
            }
        }

        // Phone validation (supports Arabic and international formats)
        if (field.type === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\u0660-\u0669\s\-\(\)]{7,15}$/;
            if (!phoneRegex.test(value)) {
                isValid = false;
                errorMessage = this.getErrorMessage('phone');
            }
        }

        // URL validation
        if (field.type === 'url' && value) {
            try {
                new URL(value);
            } catch {
                isValid = false;
                errorMessage = this.getErrorMessage('url');
            }
        }

        // Name validation (Arabic and English)
        if (field.id === 'full-name' && value) {
            const nameRegex = /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z\s]{2,50}$/;
            if (!nameRegex.test(value)) {
                isValid = false;
                errorMessage = this.getErrorMessage('name');
            }
        }

        // Show error if validation failed
        if (!isValid) {
            this.showFieldError(field, errorMessage);
        }

        return isValid;
    }

    validateForm(form, event) {
        let isFormValid = true;
        const fields = form.querySelectorAll('input, textarea, select');

        fields.forEach(field => {
            if (!this.validateField(field)) {
                isFormValid = false;
            }
        });

        if (!isFormValid) {
            event.preventDefault();
            this.showFormError(this.getErrorMessage('form_invalid'));
        }

        return isFormValid;
    }

    showFieldError(field, message) {
        field.classList.add('error');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'error-message';
        errorElement.textContent = message;
        
        if (this.isRTL) {
            errorElement.style.direction = 'rtl';
            errorElement.style.textAlign = 'right';
        }

        field.parentNode.appendChild(errorElement);
    }

    clearFieldError(field) {
        field.classList.remove('error');
        const errorMessage = field.parentNode.querySelector('.error-message');
        if (errorMessage) {
            errorMessage.remove();
        }
    }

    showFormError(message) {
        // Create or update form error message
        let errorContainer = document.querySelector('.form-error-container');
        
        if (!errorContainer) {
            errorContainer = document.createElement('div');
            errorContainer.className = 'form-error-container';
            document.body.appendChild(errorContainer);
        }

        errorContainer.innerHTML = `
            <div class="form-error-message ${this.isRTL ? 'rtl' : ''}">
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message}</span>
                <button class="close-error" onclick="this.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (errorContainer) {
                errorContainer.remove();
            }
        }, 5000);
    }

    getErrorMessage(type, fieldName = '') {
        const messages = {
            ar: {
                required: `${fieldName} مطلوب`,
                email: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
                phone: 'يرجى إدخال رقم هاتف صحيح',
                url: 'يرجى إدخال رابط صحيح',
                name: 'يرجى إدخال اسم صحيح (2-50 حرف)',
                form_invalid: 'يرجى تصحيح الأخطاء في النموذج',
                password_weak: 'كلمة المرور ضعيفة. يجب أن تحتوي على 8 أحرف على الأقل',
                passwords_mismatch: 'كلمات المرور غير متطابقة'
            },
            en: {
                required: `${fieldName} is required`,
                email: 'Please enter a valid email address',
                phone: 'Please enter a valid phone number',
                url: 'Please enter a valid URL',
                name: 'Please enter a valid name (2-50 characters)',
                form_invalid: 'Please correct the errors in the form',
                password_weak: 'Password is weak. Must contain at least 8 characters',
                passwords_mismatch: 'Passwords do not match'
            }
        };

        return messages[this.currentLanguage]?.[type] || messages.en[type] || 'Validation error';
    }

    updateFormDirection() {
        // Update all form elements direction
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            if (this.isRTL) {
                form.style.direction = 'rtl';
                form.classList.add('rtl-form');
            } else {
                form.style.direction = 'ltr';
                form.classList.remove('rtl-form');
            }
        });

        // Update input fields
        const inputs = document.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            if (this.isRTL) {
                input.style.direction = 'rtl';
                input.style.textAlign = 'right';
            } else {
                input.style.direction = 'ltr';
                input.style.textAlign = 'left';
            }
        });
    }

    // Utility methods for Arabic text processing
    normalizeArabicText(text) {
        // Normalize Arabic text (remove diacritics, etc.)
        return text
            .replace(/[\u064B-\u0652\u0670\u0640]/g, '') // Remove diacritics and tatweel
            .replace(/\u0629/g, '\u0647') // Replace teh marbuta with heh
            .replace(/[\u0622\u0623\u0625]/g, '\u0627') // Normalize alef variants
            .trim();
    }

    isArabicName(name) {
        const arabicNameRegex = /^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s]{2,}$/;
        return arabicNameRegex.test(name);
    }

    formatArabicPhone(phone) {
        // Format Arabic/Eastern Arabic numerals to Western numerals
        const arabicNumerals = '٠١٢٣٤٥٦٧٨٩';
        const westernNumerals = '0123456789';
        
        let formatted = phone;
        for (let i = 0; i < arabicNumerals.length; i++) {
            formatted = formatted.replace(new RegExp(arabicNumerals[i], 'g'), westernNumerals[i]);
        }
        
        return formatted;
    }

    // Password strength validation for Arabic users
    validatePasswordStrength(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
        
        const score = [
            password.length >= minLength,
            hasUpperCase,
            hasLowerCase,
            hasNumbers,
            hasSpecialChar
        ].filter(Boolean).length;

        return {
            isValid: score >= 3,
            score: score,
            feedback: this.getPasswordFeedback(score)
        };
    }

    getPasswordFeedback(score) {
        const feedback = {
            ar: {
                0: 'كلمة مرور ضعيفة جداً',
                1: 'كلمة مرور ضعيفة',
                2: 'كلمة مرور متوسطة',
                3: 'كلمة مرور جيدة',
                4: 'كلمة مرور قوية',
                5: 'كلمة مرور ممتازة'
            },
            en: {
                0: 'Very weak password',
                1: 'Weak password',
                2: 'Fair password',
                3: 'Good password',
                4: 'Strong password',
                5: 'Excellent password'
            }
        };

        return feedback[this.currentLanguage]?.[score] || feedback.en[score];
    }
}

// Initialize Arabic form validator
window.arabicFormValidator = new ArabicFormValidator();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ArabicFormValidator;
}
