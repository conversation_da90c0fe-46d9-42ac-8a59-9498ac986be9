<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة تسجيل الدخول</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .btn {
            background: #2563eb;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        pre {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة تسجيل الدخول</h1>
        <p>هذه الصفحة ستساعد في تحديد سبب مشكلة تسجيل الدخول</p>

        <!-- خطوة 1: فحص الاتصال -->
        <div class="test-section">
            <h3>1️⃣ فحص الاتصال بـ Supabase</h3>
            <div id="connection-result" class="status info">جاري الفحص...</div>
            <button class="btn" onclick="testConnection()">إعادة فحص الاتصال</button>
        </div>

        <!-- خطوة 2: فحص تحميل الخدمات -->
        <div class="test-section">
            <h3>2️⃣ فحص تحميل الخدمات</h3>
            <div id="services-result" class="status info">جاري الفحص...</div>
            <button class="btn" onclick="testServices()">فحص الخدمات</button>
        </div>

        <!-- خطوة 3: اختبار تسجيل الدخول -->
        <div class="test-section">
            <h3>3️⃣ اختبار تسجيل الدخول</h3>
            <form id="login-test-form">
                <div class="form-group">
                    <label for="test-email">البريد الإلكتروني:</label>
                    <input type="email" id="test-email" placeholder="أدخل البريد الإلكتروني" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="test-password">كلمة المرور:</label>
                    <input type="password" id="test-password" placeholder="أدخل كلمة المرور" value="123456">
                </div>
                <button type="submit" class="btn">اختبار تسجيل الدخول</button>
            </form>
            <div id="login-result"></div>
        </div>

        <!-- خطوة 4: إنشاء حساب تجريبي -->
        <div class="test-section">
            <h3>4️⃣ إنشاء حساب تجريبي</h3>
            <form id="signup-test-form">
                <div class="form-group">
                    <label for="signup-name">الاسم الكامل:</label>
                    <input type="text" id="signup-name" placeholder="أدخل الاسم" value="مستخدم تجريبي">
                </div>
                <div class="form-group">
                    <label for="signup-email">البريد الإلكتروني:</label>
                    <input type="email" id="signup-email" placeholder="أدخل البريد الإلكتروني" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="signup-password">كلمة المرور:</label>
                    <input type="password" id="signup-password" placeholder="أدخل كلمة المرور" value="123456">
                </div>
                <button type="submit" class="btn">إنشاء حساب تجريبي</button>
            </form>
            <div id="signup-result"></div>
        </div>

        <!-- خطوة 5: معلومات التشخيص -->
        <div class="test-section">
            <h3>5️⃣ معلومات التشخيص</h3>
            <button class="btn" onclick="showDiagnostics()">عرض معلومات التشخيص</button>
            <div id="diagnostics-result"></div>
        </div>

        <!-- خطوة 6: فحص قاعدة البيانات -->
        <div class="test-section">
            <h3>6️⃣ فحص قاعدة البيانات</h3>
            <button class="btn" onclick="testDatabase()">فحص قاعدة البيانات</button>
            <div id="database-result"></div>
        </div>
    </div>

    <!-- تحميل الملفات -->
    <script src="js/supabase-config.js"></script>
    
    <script>
        let supabaseClient = null;
        let dbService = null;

        // انتظار تحميل الصفحة
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 بدء التشخيص...');
            
            // انتظار تحميل الخدمات
            await waitForServices();
            
            // بدء الفحوصات التلقائية
            await testConnection();
            await testServices();
            
            // إعداد معالجات النماذج
            setupFormHandlers();
        });

        async function waitForServices() {
            return new Promise((resolve) => {
                let attempts = 0;
                const maxAttempts = 50;
                
                const checkServices = () => {
                    attempts++;
                    console.log(`محاولة ${attempts}: فحص الخدمات...`);
                    
                    if (window.supabase && window.dbService) {
                        supabaseClient = window.supabase;
                        dbService = window.dbService;
                        console.log('✅ تم تحميل الخدمات بنجاح');
                        resolve();
                    } else if (attempts >= maxAttempts) {
                        console.error('❌ فشل في تحميل الخدمات بعد 50 محاولة');
                        resolve();
                    } else {
                        setTimeout(checkServices, 100);
                    }
                };
                
                checkServices();
            });
        }

        async function testConnection() {
            const resultEl = document.getElementById('connection-result');
            resultEl.textContent = 'جاري فحص الاتصال...';
            resultEl.className = 'status info';

            try {
                if (!dbService) {
                    throw new Error('خدمة قاعدة البيانات غير متوفرة');
                }

                const health = await dbService.getServerHealth();
                console.log('نتيجة فحص الصحة:', health);

                if (health.status === 'healthy') {
                    resultEl.textContent = `✅ الاتصال ناجح (${health.responseTime})`;
                    resultEl.className = 'status success';
                } else {
                    resultEl.textContent = `❌ مشكلة في الاتصال: ${health.error}`;
                    resultEl.className = 'status error';
                }
            } catch (error) {
                console.error('خطأ في فحص الاتصال:', error);
                resultEl.textContent = `❌ فشل الاتصال: ${error.message}`;
                resultEl.className = 'status error';
            }
        }

        async function testServices() {
            const resultEl = document.getElementById('services-result');
            
            const services = {
                'window.supabase': !!window.supabase,
                'window.dbService': !!window.dbService,
                'dbService.supabase': !!(window.dbService && window.dbService.supabase),
                'dbService.signIn': !!(window.dbService && typeof window.dbService.signIn === 'function'),
                'dbService.signUp': !!(window.dbService && typeof window.dbService.signUp === 'function'),
                'dbService.getCurrentUser': !!(window.dbService && typeof window.dbService.getCurrentUser === 'function')
            };

            const allServicesLoaded = Object.values(services).every(Boolean);
            
            if (allServicesLoaded) {
                resultEl.innerHTML = `
                    <div class="status success">✅ جميع الخدمات محملة بنجاح</div>
                    <pre>${JSON.stringify(services, null, 2)}</pre>
                `;
            } else {
                resultEl.innerHTML = `
                    <div class="status error">❌ بعض الخدمات غير محملة</div>
                    <pre>${JSON.stringify(services, null, 2)}</pre>
                `;
            }
        }

        function setupFormHandlers() {
            document.getElementById('login-test-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await testLogin();
            });

            document.getElementById('signup-test-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await testSignup();
            });
        }

        async function testLogin() {
            const resultEl = document.getElementById('login-result');
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            const form = document.getElementById('login-test-form');

            form.classList.add('loading');
            resultEl.innerHTML = '<div class="status info">جاري اختبار تسجيل الدخول...</div>';

            try {
                console.log('🔑 محاولة تسجيل الدخول:', { email, password: '***' });
                
                if (!dbService) {
                    throw new Error('خدمة قاعدة البيانات غير متوفرة');
                }

                const { data, error } = await dbService.signIn(email, password);
                
                console.log('نتيجة تسجيل الدخول:', { data, error });

                if (error) {
                    resultEl.innerHTML = `
                        <div class="status error">❌ فشل تسجيل الدخول</div>
                        <pre>الخطأ: ${error.message}</pre>
                    `;
                } else {
                    resultEl.innerHTML = `
                        <div class="status success">✅ تم تسجيل الدخول بنجاح!</div>
                        <pre>البيانات: ${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                resultEl.innerHTML = `
                    <div class="status error">❌ خطأ غير متوقع</div>
                    <pre>الخطأ: ${error.message}</pre>
                `;
            } finally {
                form.classList.remove('loading');
            }
        }

        async function testSignup() {
            const resultEl = document.getElementById('signup-result');
            const name = document.getElementById('signup-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const form = document.getElementById('signup-test-form');

            form.classList.add('loading');
            resultEl.innerHTML = '<div class="status info">جاري إنشاء الحساب...</div>';

            try {
                console.log('📝 محاولة إنشاء حساب:', { name, email, password: '***' });
                
                if (!dbService) {
                    throw new Error('خدمة قاعدة البيانات غير متوفرة');
                }

                const { data, error } = await dbService.signUp(email, password, name);
                
                console.log('نتيجة إنشاء الحساب:', { data, error });

                if (error) {
                    resultEl.innerHTML = `
                        <div class="status error">❌ فشل إنشاء الحساب</div>
                        <pre>الخطأ: ${error.message}</pre>
                    `;
                } else {
                    resultEl.innerHTML = `
                        <div class="status success">✅ تم إنشاء الحساب بنجاح!</div>
                        <pre>البيانات: ${JSON.stringify(data, null, 2)}</pre>
                        <p><strong>ملاحظة:</strong> تحقق من بريدك الإلكتروني لتفعيل الحساب</p>
                    `;
                }
            } catch (error) {
                console.error('خطأ في إنشاء الحساب:', error);
                resultEl.innerHTML = `
                    <div class="status error">❌ خطأ غير متوقع</div>
                    <pre>الخطأ: ${error.message}</pre>
                `;
            } finally {
                form.classList.remove('loading');
            }
        }

        function showDiagnostics() {
            const resultEl = document.getElementById('diagnostics-result');
            
            const diagnostics = {
                'URL الحالي': window.location.href,
                'User Agent': navigator.userAgent,
                'وقت التحميل': new Date().toISOString(),
                'Supabase متوفر': !!window.supabase,
                'Database Service متوفر': !!window.dbService,
                'إعدادات Supabase': {
                    'URL': window.dbService?.supabase?.supabaseUrl || 'غير متوفر',
                    'Key': window.dbService?.supabase?.supabaseKey ? '[مخفي]' : 'غير متوفر'
                },
                'Console Errors': 'تحقق من وحدة تحكم المطور (F12)'
            };

            resultEl.innerHTML = `<pre>${JSON.stringify(diagnostics, null, 2)}</pre>`;
        }

        async function testDatabase() {
            const resultEl = document.getElementById('database-result');
            resultEl.innerHTML = '<div class="status info">جاري فحص قاعدة البيانات...</div>';

            try {
                if (!dbService) {
                    throw new Error('خدمة قاعدة البيانات غير متوفرة');
                }

                // اختبار الوصول للقوالب
                const { data: templates, error } = await dbService.getTemplates();
                
                if (error) {
                    resultEl.innerHTML = `
                        <div class="status error">❌ فشل الوصول لقاعدة البيانات</div>
                        <pre>الخطأ: ${error.message}</pre>
                        <p><strong>الحل المحتمل:</strong> تأكد من تشغيل SQL setup في Supabase</p>
                    `;
                } else {
                    resultEl.innerHTML = `
                        <div class="status success">✅ قاعدة البيانات تعمل بنجاح!</div>
                        <p><strong>عدد القوالب:</strong> ${templates ? templates.length : 0}</p>
                        <pre>${JSON.stringify(templates, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('خطأ في فحص قاعدة البيانات:', error);
                resultEl.innerHTML = `
                    <div class="status error">❌ خطأ في قاعدة البيانات</div>
                    <pre>الخطأ: ${error.message}</pre>
                `;
            }
        }
    </script>
</body>
</html>
