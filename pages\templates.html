<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قوالب السيرة الذاتية - Elashrafy CV</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/arabic.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .templates-main {
            padding-top: 80px;
            min-height: 100vh;
            background: var(--gray-50);
        }
        
        .templates-header {
            text-align: center;
            padding: var(--spacing-12) 0;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: var(--white);
            margin-bottom: var(--spacing-12);
        }
        
        .templates-header h1 {
            font-size: var(--font-size-4xl);
            margin-bottom: var(--spacing-4);
        }
        
        .templates-header p {
            font-size: var(--font-size-lg);
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-8);
            margin-bottom: var(--spacing-12);
        }
        
        .template-card {
            background: var(--white);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: var(--transition);
            border: 1px solid var(--gray-200);
        }
        
        .template-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }
        
        .template-preview {
            height: 400px;
            background: var(--gray-100);
            position: relative;
            overflow: hidden;
        }
        
        .template-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .template-badge {
            position: absolute;
            top: var(--spacing-3);
            right: var(--spacing-3);
            background: var(--primary-color);
            color: var(--white);
            padding: var(--spacing-1) var(--spacing-3);
            border-radius: var(--radius-full);
            font-size: var(--font-size-sm);
            font-weight: 500;
        }
        
        .template-content {
            padding: var(--spacing-6);
        }
        
        .template-title {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: var(--spacing-2);
        }
        
        .template-description {
            color: var(--gray-600);
            margin-bottom: var(--spacing-4);
            line-height: 1.6;
        }
        
        .template-features {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-2);
            margin-bottom: var(--spacing-6);
        }
        
        .template-feature {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-1);
            padding: var(--spacing-1) var(--spacing-2);
            background: var(--gray-100);
            border-radius: var(--radius);
            font-size: var(--font-size-sm);
            color: var(--gray-700);
        }
        
        .template-actions {
            display: flex;
            gap: var(--spacing-3);
        }
        
        .template-actions .btn {
            flex: 1;
            justify-content: center;
        }
        
        .filter-section {
            margin-bottom: var(--spacing-8);
            text-align: center;
        }
        
        .filter-buttons {
            display: flex;
            justify-content: center;
            gap: var(--spacing-2);
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: var(--spacing-2) var(--spacing-4);
            background: var(--white);
            border: 1px solid var(--gray-300);
            border-radius: var(--radius-full);
            cursor: pointer;
            transition: var(--transition);
            color: var(--gray-700);
            font-size: var(--font-size-sm);
        }
        
        .filter-btn.active,
        .filter-btn:hover {
            background: var(--primary-color);
            color: var(--white);
            border-color: var(--primary-color);
        }
        
        .loading-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-16);
            color: var(--gray-500);
        }
        
        .loading-placeholder i {
            font-size: var(--font-size-3xl);
            margin-bottom: var(--spacing-4);
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../index.html">
                        <i class="fas fa-file-alt"></i>
                        <span>Elashrafy CV</span>
                    </a>
                </div>
                
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="../index.html" class="nav-link">الرئيسية</a></li>
                        <li><a href="dashboard.html" class="nav-link">لوحة التحكم</a></li>
                        <li><a href="cv-builder.html" class="nav-link">منشئ السيرة الذاتية</a></li>
                        <li><a href="templates.html" class="nav-link active">القوالب</a></li>
                        <li><a href="qr-generator.html" class="nav-link">مولد QR</a></li>
                        <li><a href="nfc-manager.html" class="nav-link">إدارة NFC</a></li>
                    </ul>
                </nav>
                
                <div class="header-actions">
                    <div class="language-switcher">
                        <button class="language-btn" onclick="toggleLanguage()">
                            <i class="fas fa-globe"></i>
                            <span id="current-language">العربية</span>
                        </button>
                    </div>
                    
                    <div class="user-menu">
                        <button class="user-btn" id="user-menu-btn">
                            <i class="fas fa-user"></i>
                            <span id="user-name">المستخدم</span>
                        </button>
                        <div class="user-dropdown" id="user-dropdown">
                            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                        </div>
                    </div>
                    
                    <button class="nav-toggle" id="nav-toggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="templates-main">
        <!-- Header Section -->
        <section class="templates-header">
            <div class="container">
                <h1>قوالب السيرة الذاتية</h1>
                <p>اختر من مجموعة متنوعة من القوالب المصممة بعناية لتناسب جميع المجالات المهنية</p>
            </div>
        </section>

        <div class="container">
            <!-- Filter Section -->
            <section class="filter-section">
                <div class="filter-buttons">
                    <button class="filter-btn active" data-filter="all">جميع القوالب</button>
                    <button class="filter-btn" data-filter="modern">حديث</button>
                    <button class="filter-btn" data-filter="classic">كلاسيكي</button>
                    <button class="filter-btn" data-filter="creative">إبداعي</button>
                    <button class="filter-btn" data-filter="professional">مهني</button>
                    <button class="filter-btn" data-filter="minimal">بسيط</button>
                </div>
            </section>

            <!-- Templates Grid -->
            <section class="templates-grid" id="templates-grid">
                <!-- Loading placeholder -->
                <div class="loading-placeholder" id="loading-placeholder">
                    <i class="fas fa-spinner fa-spin"></i>
                    <p>جاري تحميل القوالب...</p>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-file-alt"></i>
                        <span>Elashrafy CV</span>
                    </div>
                    <p>منصة شاملة لإنشاء وإدارة السيرة الذاتية بتقنيات حديثة</p>
                </div>
                
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="../index.html">الرئيسية</a></li>
                        <li><a href="cv-builder.html">منشئ السيرة الذاتية</a></li>
                        <li><a href="templates.html">القوالب</a></li>
                        <li><a href="dashboard.html">لوحة التحكم</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>الدعم</h4>
                    <ul>
                        <li><a href="#help">المساعدة</a></li>
                        <li><a href="#contact">اتصل بنا</a></li>
                        <li><a href="#privacy">سياسة الخصوصية</a></li>
                        <li><a href="#terms">شروط الاستخدام</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>تابعنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Elashrafy CV. جميع الحقوق محفوظة.</p>
                <p>تم التطوير بواسطة <a href="#" class="footer-credit">Elashrafy</a></p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../js/supabase-config.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/db-service.js"></script>
    <script src="../js/translations.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        // Templates page functionality
        class TemplatesPage {
            constructor() {
                this.templates = [];
                this.currentFilter = 'all';
                this.init();
            }

            async init() {
                await this.loadTemplates();
                this.setupEventListeners();
                this.renderTemplates();
            }

            async loadTemplates() {
                try {
                    // Mock templates data - replace with actual API call
                    this.templates = [
                        {
                            id: 1,
                            name: 'مهني حديث',
                            nameEn: 'Modern Professional',
                            description: 'تصميم نظيف وحديث مثالي للمهنيين التقنيين والمطورين',
                            category: 'modern',
                            features: ['دعم RTL', 'تصميم متجاوب', 'ألوان حديثة'],
                            preview: '../assets/templates/modern-preview.jpg',
                            isPremium: false
                        },
                        {
                            id: 2,
                            name: 'تنفيذي كلاسيكي',
                            nameEn: 'Classic Executive',
                            description: 'تخطيط تقليدي أنيق مثالي للمناصب التنفيذية والإدارية',
                            category: 'classic',
                            features: ['تخطيط تقليدي', 'خطوط أنيقة', 'مظهر احترافي'],
                            preview: '../assets/templates/classic-preview.jpg',
                            isPremium: false
                        },
                        {
                            id: 3,
                            name: 'مصمم إبداعي',
                            nameEn: 'Creative Designer',
                            description: 'تصميم جريء وإبداعي للمصممين والفنانين',
                            category: 'creative',
                            features: ['ألوان جريئة', 'تخطيط إبداعي', 'عناصر بصرية'],
                            preview: '../assets/templates/creative-preview.jpg',
                            isPremium: true
                        }
                    ];
                } catch (error) {
                    console.error('Error loading templates:', error);
                }
            }

            setupEventListeners() {
                // Filter buttons
                const filterButtons = document.querySelectorAll('.filter-btn');
                filterButtons.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        filterButtons.forEach(b => b.classList.remove('active'));
                        e.target.classList.add('active');
                        this.currentFilter = e.target.dataset.filter;
                        this.renderTemplates();
                    });
                });
            }

            renderTemplates() {
                const grid = document.getElementById('templates-grid');
                const loading = document.getElementById('loading-placeholder');
                
                loading.style.display = 'none';
                
                const filteredTemplates = this.currentFilter === 'all' 
                    ? this.templates 
                    : this.templates.filter(t => t.category === this.currentFilter);

                grid.innerHTML = filteredTemplates.map(template => `
                    <div class="template-card" data-category="${template.category}">
                        <div class="template-preview">
                            <img src="${template.preview}" alt="${template.name}" onerror="this.src='../assets/placeholder-template.jpg'">
                            ${template.isPremium ? '<div class="template-badge">مميز</div>' : ''}
                        </div>
                        <div class="template-content">
                            <h3 class="template-title">${template.name}</h3>
                            <p class="template-description">${template.description}</p>
                            <div class="template-features">
                                ${template.features.map(feature => `
                                    <span class="template-feature">
                                        <i class="fas fa-check"></i>
                                        ${feature}
                                    </span>
                                `).join('')}
                            </div>
                            <div class="template-actions">
                                <button class="btn btn-outline" onclick="previewTemplate(${template.id})">
                                    <i class="fas fa-eye"></i>
                                    معاينة
                                </button>
                                <button class="btn btn-primary" onclick="useTemplate(${template.id})">
                                    <i class="fas fa-plus"></i>
                                    استخدام
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
            }
        }

        // Template actions
        function previewTemplate(templateId) {
            // Open template preview in modal or new tab
            window.open(`cv-builder.html?template=${templateId}&preview=true`, '_blank');
        }

        function useTemplate(templateId) {
            // Redirect to CV builder with selected template
            window.location.href = `cv-builder.html?template=${templateId}`;
        }

        // Initialize templates page
        document.addEventListener('DOMContentLoaded', () => {
            window.templatesPage = new TemplatesPage();
        });
    </script>
</body>
</html>
