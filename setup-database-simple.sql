-- إعداد قاعدة البيانات البسيط لـ Elashrafy CV
-- نفذ هذا الكود في Supabase SQL Editor

-- تفعيل الإضافات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- إنشاء جدول الملفات الشخصية
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    avatar_url TEXT,
    phone TEXT,
    location TEXT,
    bio TEXT,
    website TEXT,
    linkedin TEXT,
    github TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول قوالب السيرة الذاتية
CREATE TABLE IF NOT EXISTS cv_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    category TEXT NOT NULL DEFAULT 'modern',
    template_data JSONB NOT NULL DEFAULT '{}',
    preview_image TEXT,
    is_active BOOLEAN DEFAULT true,
    is_premium BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول السير الذاتية
CREATE TABLE IF NOT EXISTS cvs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    cv_data JSONB NOT NULL DEFAULT '{}',
    template_id UUID REFERENCES cv_templates(id),
    is_public BOOLEAN DEFAULT false,
    views INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول رموز QR
CREATE TABLE IF NOT EXISTS qr_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    qr_url TEXT NOT NULL,
    qr_image TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول بطاقات NFC
CREATE TABLE IF NOT EXISTS nfc_cards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    card_name TEXT NOT NULL,
    description TEXT,
    nfc_url TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول التحليلات
CREATE TABLE IF NOT EXISTS cv_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    event_type TEXT NOT NULL,
    event_data JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- تفعيل Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE cvs ENABLE ROW LEVEL SECURITY;
ALTER TABLE qr_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE nfc_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE cv_analytics ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للملفات الشخصية
CREATE POLICY "Users can view own profile" ON profiles 
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles 
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

-- سياسات الأمان للسير الذاتية
CREATE POLICY "Users can view own CVs" ON cvs 
    FOR SELECT USING (auth.uid() = user_id OR is_public = true);

CREATE POLICY "Users can insert own CVs" ON cvs 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own CVs" ON cvs 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own CVs" ON cvs 
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لرموز QR
CREATE POLICY "Users can view own QR codes" ON qr_codes 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own QR codes" ON qr_codes 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own QR codes" ON qr_codes 
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لبطاقات NFC
CREATE POLICY "Users can view own NFC cards" ON nfc_cards 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own NFC cards" ON nfc_cards 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own NFC cards" ON nfc_cards 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own NFC cards" ON nfc_cards 
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للتحليلات
CREATE POLICY "Users can view analytics for own CVs" ON cv_analytics 
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM cvs 
            WHERE cvs.id = cv_analytics.cv_id 
            AND cvs.user_id = auth.uid()
        )
    );

CREATE POLICY "Anyone can insert analytics" ON cv_analytics 
    FOR INSERT WITH CHECK (true);

-- القوالب متاحة للقراءة للجميع
CREATE POLICY "Templates are public" ON cv_templates 
    FOR SELECT USING (is_active = true);

-- دالة لتحديث التوقيت تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء المشغلات للتحديث التلقائي
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cvs_updated_at 
    BEFORE UPDATE ON cvs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cv_templates_updated_at 
    BEFORE UPDATE ON cv_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_nfc_cards_updated_at 
    BEFORE UPDATE ON nfc_cards 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- دالة لإنشاء ملف شخصي تلقائياً عند التسجيل
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- مشغل لإنشاء الملف الشخصي تلقائياً
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- إدراج القوالب الافتراضية
INSERT INTO cv_templates (name, name_ar, description, description_ar, category, template_data, is_active) VALUES
(
    'Modern Professional',
    'مهني حديث',
    'Clean and modern design perfect for tech professionals',
    'تصميم نظيف وحديث مثالي للمهنيين التقنيين',
    'modern',
    '{"colors": {"primary": "#2563eb", "secondary": "#64748b"}, "fonts": {"primary": "Cairo", "secondary": "Inter"}}',
    true
),
(
    'Classic Executive',
    'تنفيذي كلاسيكي',
    'Traditional layout ideal for executive positions',
    'تخطيط تقليدي مثالي للمناصب التنفيذية',
    'classic',
    '{"colors": {"primary": "#f59e0b", "secondary": "#6b7280"}, "fonts": {"primary": "Noto Sans Arabic", "secondary": "Georgia"}}',
    true
),
(
    'Creative Designer',
    'مصمم إبداعي',
    'Bold and creative design for creative professionals',
    'تصميم جريء وإبداعي للمهنيين المبدعين',
    'creative',
    '{"colors": {"primary": "#10b981", "secondary": "#8b5cf6"}, "fonts": {"primary": "Cairo", "secondary": "Poppins"}}',
    true
);

-- منح الصلاحيات المطلوبة
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
