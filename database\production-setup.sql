-- إعد<PERSON> قاعدة البيانات الإنتاجية لتطبيق Elashrafy CV
-- تنفيذ هذا الكود في Supabase SQL Editor

-- تفعيل الإضافات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- إنشاء جدول الملفات الشخصية
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT NOT NULL,
    avatar_url TEXT,
    phone TEXT,
    location TEXT,
    bio TEXT,
    website TEXT,
    linkedin TEXT,
    github TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول قوالب السيرة الذاتية
CREATE TABLE IF NOT EXISTS public.cv_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    category TEXT NOT NULL DEFAULT 'modern',
    template_data JSONB NOT NULL DEFAULT '{}',
    preview_image TEXT,
    is_active BOOLEAN DEFAULT true,
    is_premium BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول السير الذاتية
CREATE TABLE IF NOT EXISTS public.cvs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    cv_data JSONB NOT NULL DEFAULT '{}',
    template_id UUID REFERENCES cv_templates(id),
    is_public BOOLEAN DEFAULT false,
    views INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول رموز QR
CREATE TABLE IF NOT EXISTS public.qr_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    qr_url TEXT NOT NULL,
    qr_image TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول بطاقات NFC
CREATE TABLE IF NOT EXISTS public.nfc_cards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    card_name TEXT NOT NULL,
    description TEXT,
    nfc_url TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول التحليلات
CREATE TABLE IF NOT EXISTS public.cv_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    event_type TEXT NOT NULL,
    event_data JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- تفعيل Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cvs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.qr_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.nfc_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.cv_analytics ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للملفات الشخصية
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;

CREATE POLICY "Users can view own profile" ON public.profiles 
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles 
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

-- سياسات الأمان للسير الذاتية
DROP POLICY IF EXISTS "Users can view own CVs" ON public.cvs;
DROP POLICY IF EXISTS "Users can insert own CVs" ON public.cvs;
DROP POLICY IF EXISTS "Users can update own CVs" ON public.cvs;
DROP POLICY IF EXISTS "Users can delete own CVs" ON public.cvs;

CREATE POLICY "Users can view own CVs" ON public.cvs 
    FOR SELECT USING (auth.uid() = user_id OR is_public = true);

CREATE POLICY "Users can insert own CVs" ON public.cvs 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own CVs" ON public.cvs 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own CVs" ON public.cvs 
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لرموز QR
DROP POLICY IF EXISTS "Users can view own QR codes" ON public.qr_codes;
DROP POLICY IF EXISTS "Users can insert own QR codes" ON public.qr_codes;
DROP POLICY IF EXISTS "Users can delete own QR codes" ON public.qr_codes;

CREATE POLICY "Users can view own QR codes" ON public.qr_codes 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own QR codes" ON public.qr_codes 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own QR codes" ON public.qr_codes 
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لبطاقات NFC
DROP POLICY IF EXISTS "Users can view own NFC cards" ON public.nfc_cards;
DROP POLICY IF EXISTS "Users can insert own NFC cards" ON public.nfc_cards;
DROP POLICY IF EXISTS "Users can update own NFC cards" ON public.nfc_cards;
DROP POLICY IF EXISTS "Users can delete own NFC cards" ON public.nfc_cards;

CREATE POLICY "Users can view own NFC cards" ON public.nfc_cards 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own NFC cards" ON public.nfc_cards 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own NFC cards" ON public.nfc_cards 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own NFC cards" ON public.nfc_cards 
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للتحليلات
DROP POLICY IF EXISTS "Users can view analytics for own CVs" ON public.cv_analytics;
DROP POLICY IF EXISTS "Anyone can insert analytics" ON public.cv_analytics;

CREATE POLICY "Users can view analytics for own CVs" ON public.cv_analytics 
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM cvs 
            WHERE cvs.id = cv_analytics.cv_id 
            AND cvs.user_id = auth.uid()
        )
    );

CREATE POLICY "Anyone can insert analytics" ON public.cv_analytics 
    FOR INSERT WITH CHECK (true);

-- القوالب متاحة للقراءة للجميع
DROP POLICY IF EXISTS "Templates are public" ON public.cv_templates;
CREATE POLICY "Templates are public" ON public.cv_templates 
    FOR SELECT USING (is_active = true);

-- دالة لتحديث التوقيت تلقائياً
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء المشغلات للتحديث التلقائي
DROP TRIGGER IF EXISTS update_profiles_updated_at ON public.profiles;
DROP TRIGGER IF EXISTS update_cvs_updated_at ON public.cvs;
DROP TRIGGER IF EXISTS update_cv_templates_updated_at ON public.cv_templates;
DROP TRIGGER IF EXISTS update_nfc_cards_updated_at ON public.nfc_cards;

CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON public.profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cvs_updated_at 
    BEFORE UPDATE ON public.cvs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cv_templates_updated_at 
    BEFORE UPDATE ON public.cv_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_nfc_cards_updated_at 
    BEFORE UPDATE ON public.nfc_cards 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- دالة لإنشاء ملف شخصي تلقائياً عند التسجيل
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- مشغل لإنشاء الملف الشخصي تلقائياً
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- إدراج القوالب الافتراضية
INSERT INTO public.cv_templates (name, name_ar, description, description_ar, category, template_data, is_active) VALUES
(
    'Modern Professional',
    'مهني حديث',
    'Clean and modern design perfect for tech professionals',
    'تصميم نظيف وحديث مثالي للمهنيين التقنيين',
    'modern',
    '{"colors": {"primary": "#2563eb", "secondary": "#64748b"}, "fonts": {"primary": "Cairo", "secondary": "Inter"}}',
    true
),
(
    'Classic Executive',
    'تنفيذي كلاسيكي',
    'Traditional layout ideal for executive positions',
    'تخطيط تقليدي مثالي للمناصب التنفيذية',
    'classic',
    '{"colors": {"primary": "#f59e0b", "secondary": "#6b7280"}, "fonts": {"primary": "Noto Sans Arabic", "secondary": "Georgia"}}',
    true
),
(
    'Creative Designer',
    'مصمم إبداعي',
    'Bold and creative design for creative professionals',
    'تصميم جريء وإبداعي للمهنيين المبدعين',
    'creative',
    '{"colors": {"primary": "#10b981", "secondary": "#8b5cf6"}, "fonts": {"primary": "Cairo", "secondary": "Poppins"}}',
    true
),
(
    'Minimal Clean',
    'بسيط نظيف',
    'Minimalist design focusing on content',
    'تصميم بسيط يركز على المحتوى',
    'minimal',
    '{"colors": {"primary": "#1f2937", "secondary": "#9ca3af"}, "fonts": {"primary": "Inter", "secondary": "Cairo"}}',
    true
),
(
    'Professional Corporate',
    'مؤسسي مهني',
    'Corporate style perfect for business professionals',
    'أسلوب مؤسسي مثالي للمهنيين في الأعمال',
    'professional',
    '{"colors": {"primary": "#1e40af", "secondary": "#475569"}, "fonts": {"primary": "Cairo", "secondary": "Roboto"}}',
    true
)
ON CONFLICT DO NOTHING;

-- منح الصلاحيات المطلوبة
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
