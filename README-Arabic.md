# منشئ السيرة الذاتية العربي - Elashrafy CV

## نظرة عامة

منشئ السيرة الذاتية العربي هو تطبيق ويب شامل يدعم اللغة العربية بالكامل مع تخطيط RTL (من اليمين إلى اليسار) وجميع المميزات المطلوبة لإنشاء سيرة ذاتية مهنية.

## المميزات الرئيسية

### 🌐 دعم اللغة العربية الكامل
- **تخطيط RTL**: دعم كامل للتخطيط من اليمين إلى اليسار
- **خطوط عربية**: استخدام خطوط Cairo وNoto Sans Arabic
- **ترجمة ديناميكية**: تبديل فوري بين العربية والإنجليزية
- **إدخال نص عربي**: دعم كامل لإدخال النصوص العربية

### 📝 نظام النماذج المتقدم
- **التحقق العربي**: رسائل خطأ باللغة العربية
- **تنسيق الأرقام**: دعم الأرقام العربية والهندية
- **التحقق من الأسماء**: دعم الأسماء العربية والإنجليزية
- **تنسيق الهاتف**: دعم أرقام الهواتف العربية

### 🎨 قوالب مهنية
- **قالب حديث**: تصميم نظيف ومعاصر
- **قالب كلاسيكي**: تخطيط تقليدي مهني
- **قالب إبداعي**: تصميم جريء للمبدعين
- **دعم RTL**: جميع القوالب تدعم التخطيط العربي

### 📄 تصدير PDF متقدم
- **نصوص عربية**: عرض صحيح للنصوص العربية في PDF
- **تخطيط RTL**: حفظ التخطيط الصحيح في PDF
- **خطوط مدمجة**: دعم الخطوط العربية في PDF
- **جودة عالية**: تصدير بجودة طباعة احترافية

## الملفات والمكونات

### ملفات CSS
- `css/arabic.css` - أنماط RTL والخطوط العربية
- `css/style.css` - الأنماط الأساسية
- `css/cv-builder.css` - أنماط منشئ السيرة الذاتية
- `css/responsive.css` - التصميم المتجاوب

### ملفات JavaScript
- `js/translations.js` - نظام الترجمة والتبديل بين اللغات
- `js/arabic-validation.js` - التحقق من النماذج العربية
- `js/arabic-templates.js` - نظام القوالب العربية
- `js/arabic-pdf.js` - تصدير PDF مع دعم العربية
- `js/cv-builder.js` - منطق منشئ السيرة الذاتية
- `js/supabase-config.js` - إعدادات قاعدة البيانات

### صفحات HTML
- `index.html` - الصفحة الرئيسية (عربية)
- `pages/cv-builder.html` - منشئ السيرة الذاتية
- `test-arabic.html` - صفحة اختبار المميزات العربية

## التقنيات المستخدمة

### Frontend
- **HTML5** مع دعم RTL
- **CSS3** مع Flexbox وGrid
- **JavaScript ES6+** للتفاعل
- **Font Awesome** للأيقونات
- **Google Fonts** للخطوط العربية

### Backend
- **Supabase** لقاعدة البيانات والمصادقة
- **PostgreSQL** لتخزين البيانات
- **Real-time subscriptions** للتحديثات المباشرة

### مكتبات PDF
- **jsPDF** لإنشاء ملفات PDF
- **html2canvas** لتحويل HTML إلى صور
- **Arabic PDF support** للنصوص العربية

## كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
# افتح الملف في متصفح الويب
open index.html
```

### 2. اختبار المميزات
```bash
# افتح صفحة الاختبار
open test-arabic.html
```

### 3. إنشاء سيرة ذاتية
1. انتقل إلى منشئ السيرة الذاتية
2. املأ المعلومات الشخصية
3. أضف الخبرات والتعليم
4. اختر قالباً مناسباً
5. صدّر إلى PDF

## المميزات التقنية

### نظام الترجمة
```javascript
// تبديل اللغة
translationManager.switchLanguage('ar');

// الحصول على ترجمة
const text = translationManager.get('nav.home');
```

### التحقق من النماذج
```javascript
// التحقق من حقل
arabicFormValidator.validateField(inputElement);

// التحقق من نموذج كامل
arabicFormValidator.validateForm(formElement);
```

### إنشاء قوالب
```javascript
// إنشاء معاينة
const preview = arabicTemplateManager.generatePreview('modern', cvData);

// الحصول على قائمة القوالب
const templates = arabicTemplateManager.getTemplatesList();
```

### تصدير PDF
```javascript
// تصدير إلى PDF
arabicPDFExporter.exportToPDF(cvData, 'modern')
    .then(result => {
        if (result.success) {
            console.log('تم التصدير بنجاح');
        }
    });
```

## إعدادات قاعدة البيانات

### جداول Supabase
```sql
-- جدول الملفات الشخصية
CREATE TABLE profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE,
    email TEXT,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- جدول السير الذاتية
CREATE TABLE cvs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    template_id TEXT,
    cv_data JSONB,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- جدول القوالب
CREATE TABLE cv_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    template_data JSONB,
    preview_image TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## الاختبار والتطوير

### اختبار المميزات
1. افتح `test-arabic.html`
2. اختبر تبديل اللغة
3. اختبر التحقق من النماذج
4. اختبر إنشاء المعاينة
5. اختبر تصدير PDF

### إضافة ترجمات جديدة
```javascript
// في ملف translations.js
translations.ar['new.key'] = 'النص العربي';
translations.en['new.key'] = 'English Text';
```

### إضافة قالب جديد
```javascript
// في ملف arabic-templates.js
templates.newTemplate = {
    name: 'قالب جديد',
    nameEn: 'New Template',
    description: 'وصف القالب',
    colors: { primary: '#color' }
};
```

## المتطلبات

### متصفح الويب
- Chrome 80+ أو Firefox 75+ أو Safari 13+
- دعم JavaScript ES6+
- دعم CSS Grid وFlexbox

### الخطوط
- Cairo (Google Fonts)
- Noto Sans Arabic (Google Fonts)
- Font Awesome 6.4+

## الدعم والمساعدة

### معلومات الاتصال
- **المطور**: محمد الأشرافي
- **الهاتف**: 01014840269
- **البريد الإلكتروني**: <EMAIL>

### المشاكل الشائعة
1. **عدم ظهور الخطوط العربية**: تأكد من تحميل Google Fonts
2. **مشاكل RTL**: تحقق من إعداد `dir="rtl"` في HTML
3. **أخطاء PDF**: تأكد من تحميل مكتبات jsPDF

## الترخيص

جميع الحقوق محفوظة © 2024 محمد الأشرافي - Elashrafy CV

---

**ملاحظة**: هذا التطبيق مصمم خصيصاً للمستخدمين العرب مع دعم كامل للغة العربية وتخطيط RTL.
