<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منشئ السيرة الذاتية - الأشرافي CV</title>
    <meta name="description" content="أنشئ سيرة ذاتية مهنية باستخدام منشئنا البديهي واقتراحات الذكاء الاصطناعي.">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/icons/favicon.ico">

    <!-- CSS -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/responsive.css">
    <link rel="stylesheet" href="../css/cv-builder.css">
    <link rel="stylesheet" href="../css/arabic.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts - Arabic Support -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- jsPDF for PDF export with Arabic support -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <!-- Arabic PDF support -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-arabic/1.0.0/jspdf-arabic.min.js"></script>
</head>
<body class="rtl-layout">
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <a href="../index.html">
                        <h1><i class="fas fa-file-alt"></i> Elashrafy CV</h1>
                    </a>
                </div>
                
                <div class="nav-menu" id="nav-menu">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="../index.html" class="nav-link">Home</a>
                        </li>
                        <li class="nav-item">
                            <a href="dashboard.html" class="nav-link">Dashboard</a>
                        </li>
                        <li class="nav-item">
                            <a href="templates.html" class="nav-link">Templates</a>
                        </li>
                    </ul>
                </div>
                
                <div class="nav-auth">
                    <div class="cv-actions">
                        <button class="btn btn-outline" id="save-cv-btn">
                            <i class="fas fa-save"></i> Save
                        </button>
                        <button class="btn btn-primary" id="export-pdf-btn">
                            <i class="fas fa-download"></i> Export PDF
                        </button>
                    </div>
                    
                    <div class="user-menu" id="user-menu">
                        <img src="" alt="Avatar" class="user-avatar" id="user-avatar">
                        <span class="user-name" id="user-name"></span>
                        <div class="dropdown">
                            <button class="dropdown-btn"><i class="fas fa-chevron-down"></i></button>
                            <div class="dropdown-content">
                                <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                                <a href="profile.html"><i class="fas fa-user"></i> Profile</a>
                                <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="nav-toggle" id="nav-toggle">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="cv-builder-main">
        <div class="cv-builder-container">
            <!-- Sidebar -->
            <aside class="cv-sidebar">
                <div class="sidebar-header">
                    <h2 class="arabic-heading" data-translate="cv.title">منشئ السيرة الذاتية</h2>
                    <button class="sidebar-toggle" id="sidebar-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>

                <!-- CV Info -->
                <div class="sidebar-section">
                    <h3 class="arabic-heading" data-translate="cv.cv_info">معلومات السيرة الذاتية</h3>
                    <div class="form-group">
                        <label for="cv-title" class="arabic-text" data-translate="cv.cv_title">عنوان السيرة الذاتية</label>
                        <input type="text" id="cv-title" placeholder="مثال: سيرة ذاتية لمطور برمجيات" value="سيرتي الذاتية المهنية" class="arabic-text">
                    </div>
                    <div class="form-group">
                        <label for="cv-template" class="arabic-text" data-translate="cv.template">القالب</label>
                        <select id="cv-template" class="arabic-text">
                            <option value="modern" data-translate="templates.modern.name">مهني حديث</option>
                            <option value="classic" data-translate="templates.classic.name">تنفيذي كلاسيكي</option>
                            <option value="creative" data-translate="templates.creative.name">مصمم إبداعي</option>
                        </select>
                    </div>
                </div>

                <!-- Sections -->
                <div class="sidebar-section">
                    <h3 class="arabic-heading" data-translate="cv.sections">أقسام السيرة الذاتية</h3>
                    <div class="sections-list">
                        <div class="section-item active" data-section="personal">
                            <i class="fas fa-user"></i>
                            <span class="arabic-text" data-translate="cv.personal_info">المعلومات الشخصية</span>
                        </div>
                        <div class="section-item" data-section="summary">
                            <i class="fas fa-align-left"></i>
                            <span class="arabic-text" data-translate="cv.professional_summary">الملخص المهني</span>
                        </div>
                        <div class="section-item" data-section="experience">
                            <i class="fas fa-briefcase"></i>
                            <span class="arabic-text" data-translate="cv.work_experience">الخبرة العملية</span>
                        </div>
                        <div class="section-item" data-section="education">
                            <i class="fas fa-graduation-cap"></i>
                            <span class="arabic-text" data-translate="cv.education">التعليم</span>
                        </div>
                        <div class="section-item" data-section="skills">
                            <i class="fas fa-cogs"></i>
                            <span class="arabic-text" data-translate="cv.skills">المهارات</span>
                        </div>
                        <div class="section-item" data-section="projects">
                            <i class="fas fa-project-diagram"></i>
                            <span class="arabic-text" data-translate="cv.projects">المشاريع</span>
                        </div>
                        <div class="section-item" data-section="certifications">
                            <i class="fas fa-certificate"></i>
                            <span class="arabic-text" data-translate="cv.certifications">الشهادات</span>
                        </div>
                        <div class="section-item" data-section="languages">
                            <i class="fas fa-language"></i>
                            <span class="arabic-text" data-translate="cv.languages">اللغات</span>
                        </div>
                    </div>
                </div>

                <!-- AI Assistant -->
                <div class="sidebar-section">
                    <h3>AI Assistant</h3>
                    <div class="ai-suggestions" id="ai-suggestions">
                        <div class="ai-suggestion">
                            <i class="fas fa-lightbulb"></i>
                            <p>Add more specific achievements to your work experience</p>
                        </div>
                        <div class="ai-suggestion">
                            <i class="fas fa-lightbulb"></i>
                            <p>Consider adding relevant certifications</p>
                        </div>
                    </div>
                    <button class="btn btn-outline btn-full" id="get-ai-suggestions">
                        <i class="fas fa-robot"></i> Get AI Suggestions
                    </button>
                </div>
            </aside>

            <!-- Editor -->
            <div class="cv-editor">
                <div class="editor-header">
                    <div class="editor-tabs">
                        <button class="tab-btn active" data-tab="edit">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="tab-btn" data-tab="preview">
                            <i class="fas fa-eye"></i> Preview
                        </button>
                    </div>
                    <div class="editor-actions">
                        <button class="btn btn-outline" id="undo-btn" disabled>
                            <i class="fas fa-undo"></i>
                        </button>
                        <button class="btn btn-outline" id="redo-btn" disabled>
                            <i class="fas fa-redo"></i>
                        </button>
                    </div>
                </div>

                <!-- Edit Tab -->
                <div class="editor-content" id="edit-tab">
                    <!-- Personal Information Section -->
                    <div class="editor-section active" id="personal-section">
                        <h3 class="arabic-heading" data-translate="cv.personal_info">المعلومات الشخصية</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="full-name" class="arabic-text" data-translate="form.full_name">الاسم الكامل *</label>
                                <input type="text" id="full-name" placeholder="أحمد محمد" required class="arabic-text" data-field-name="الاسم الكامل">
                            </div>
                            <div class="form-group">
                                <label for="job-title" class="arabic-text" data-translate="form.job_title">المسمى الوظيفي *</label>
                                <input type="text" id="job-title" placeholder="مطور برمجيات" required class="arabic-text" data-field-name="المسمى الوظيفي">
                            </div>
                            <div class="form-group">
                                <label for="email" class="arabic-text" data-translate="form.email">البريد الإلكتروني *</label>
                                <input type="email" id="email" placeholder="<EMAIL>" required class="arabic-text" data-field-name="البريد الإلكتروني">
                            </div>
                            <div class="form-group">
                                <label for="phone" class="arabic-text" data-translate="form.phone">رقم الهاتف</label>
                                <input type="tel" id="phone" placeholder="٠١٠١٤٨٤٠٢٦٩" class="arabic-text">
                            </div>
                            <div class="form-group">
                                <label for="location" class="arabic-text" data-translate="form.location">الموقع</label>
                                <input type="text" id="location" placeholder="القاهرة، مصر" class="arabic-text">
                            </div>
                            <div class="form-group">
                                <label for="website" class="arabic-text" data-translate="form.website">الموقع الإلكتروني</label>
                                <input type="url" id="website" placeholder="https://ahmed.com" class="arabic-text">
                            </div>
                            <div class="form-group">
                                <label for="linkedin" class="arabic-text" data-translate="form.linkedin">لينكد إن</label>
                                <input type="url" id="linkedin" placeholder="https://linkedin.com/in/ahmed" class="arabic-text">
                            </div>
                            <div class="form-group">
                                <label for="github" class="arabic-text" data-translate="form.github">جيت هاب</label>
                                <input type="url" id="github" placeholder="https://github.com/ahmed" class="arabic-text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="photo-upload" class="arabic-text" data-translate="form.profile_photo">الصورة الشخصية</label>
                            <div class="photo-upload">
                                <input type="file" id="photo-upload" accept="image/*" style="display: none;">
                                <div class="photo-preview" id="photo-preview">
                                    <i class="fas fa-camera"></i>
                                    <span class="arabic-text" data-translate="form.upload_photo">انقر لرفع الصورة</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Professional Summary Section -->
                    <div class="editor-section" id="summary-section">
                        <h3>Professional Summary</h3>
                        <div class="form-group">
                            <label for="professional-summary">Summary</label>
                            <textarea id="professional-summary" rows="6" placeholder="Write a compelling professional summary that highlights your key achievements and career goals..."></textarea>
                            <div class="form-help">
                                <i class="fas fa-info-circle"></i>
                                Write 3-4 sentences highlighting your experience, skills, and career objectives.
                            </div>
                        </div>
                        <button class="btn btn-outline" id="generate-summary">
                            <i class="fas fa-magic"></i> Generate with AI
                        </button>
                    </div>

                    <!-- Work Experience Section -->
                    <div class="editor-section" id="experience-section">
                        <div class="section-header">
                            <h3>Work Experience</h3>
                            <button class="btn btn-primary" id="add-experience">
                                <i class="fas fa-plus"></i> Add Experience
                            </button>
                        </div>
                        <div class="experience-list" id="experience-list">
                            <!-- Experience items will be added here -->
                        </div>
                    </div>

                    <!-- Education Section -->
                    <div class="editor-section" id="education-section">
                        <div class="section-header">
                            <h3>Education</h3>
                            <button class="btn btn-primary" id="add-education">
                                <i class="fas fa-plus"></i> Add Education
                            </button>
                        </div>
                        <div class="education-list" id="education-list">
                            <!-- Education items will be added here -->
                        </div>
                    </div>

                    <!-- Skills Section -->
                    <div class="editor-section" id="skills-section">
                        <div class="section-header">
                            <h3>Skills</h3>
                            <button class="btn btn-primary" id="add-skill">
                                <i class="fas fa-plus"></i> Add Skill
                            </button>
                        </div>
                        <div class="skills-list" id="skills-list">
                            <!-- Skills will be added here -->
                        </div>
                        <div class="form-group">
                            <label for="new-skill">Add Skill</label>
                            <div class="skill-input">
                                <input type="text" id="new-skill" placeholder="e.g., JavaScript">
                                <select id="skill-level">
                                    <option value="beginner">Beginner</option>
                                    <option value="intermediate">Intermediate</option>
                                    <option value="advanced">Advanced</option>
                                    <option value="expert">Expert</option>
                                </select>
                                <button class="btn btn-outline" id="add-skill-btn">Add</button>
                            </div>
                        </div>
                    </div>

                    <!-- Projects Section -->
                    <div class="editor-section" id="projects-section">
                        <div class="section-header">
                            <h3>Projects</h3>
                            <button class="btn btn-primary" id="add-project">
                                <i class="fas fa-plus"></i> Add Project
                            </button>
                        </div>
                        <div class="projects-list" id="projects-list">
                            <!-- Projects will be added here -->
                        </div>
                    </div>

                    <!-- Certifications Section -->
                    <div class="editor-section" id="certifications-section">
                        <div class="section-header">
                            <h3>Certifications</h3>
                            <button class="btn btn-primary" id="add-certification">
                                <i class="fas fa-plus"></i> Add Certification
                            </button>
                        </div>
                        <div class="certifications-list" id="certifications-list">
                            <!-- Certifications will be added here -->
                        </div>
                    </div>

                    <!-- Languages Section -->
                    <div class="editor-section" id="languages-section">
                        <div class="section-header">
                            <h3>Languages</h3>
                            <button class="btn btn-primary" id="add-language">
                                <i class="fas fa-plus"></i> Add Language
                            </button>
                        </div>
                        <div class="languages-list" id="languages-list">
                            <!-- Languages will be added here -->
                        </div>
                    </div>
                </div>

                <!-- Preview Tab -->
                <div class="editor-content" id="preview-tab" style="display: none;">
                    <div class="cv-preview" id="cv-preview">
                        <!-- CV preview will be rendered here -->
                        <div class="preview-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Generating preview...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Templates Modal -->
    <div id="templates-modal" class="modal">
        <div class="modal-content" style="max-width: 1000px;">
            <span class="close">&times;</span>
            <h2>Choose a Template</h2>
            <div class="templates-grid" id="templates-grid">
                <!-- Templates will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/supabase-config.js"></script>
    <script src="../js/translations.js"></script>
    <script src="../js/arabic-validation.js"></script>
    <script src="../js/arabic-templates.js"></script>
    <script src="../js/arabic-pdf.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/main.js"></script>
    <script src="../js/cv-builder.js"></script>
</body>
</html>
