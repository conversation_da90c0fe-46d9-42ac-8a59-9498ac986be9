<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deployment Test - Elashrafy CV</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.5);
        }
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.5);
        }
        .info {
            background: rgba(59, 130, 246, 0.2);
            border: 1px solid rgba(59, 130, 246, 0.5);
        }
        .btn {
            background: #3b82f6;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #2563eb;
        }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <h1>🚀 Elashrafy CV - Deployment Test</h1>
        <p>This page verifies that the application is properly deployed and configured.</p>

        <div id="deployment-status" class="status info">
            🔄 Testing deployment...
        </div>

        <h3>🧪 Test Results</h3>
        <div id="test-results"></div>

        <h3>🔗 Quick Links</h3>
        <a href="index.html" class="btn">Main Application</a>
        <a href="pages/dashboard.html" class="btn">Dashboard</a>
        <a href="pages/cv-builder.html" class="btn">CV Builder</a>

        <h3>📊 System Information</h3>
        <div id="system-info"></div>
    </div>

    <script>
        // Test deployment configuration
        async function testDeployment() {
            const results = [];
            const statusEl = document.getElementById('deployment-status');
            const resultsEl = document.getElementById('test-results');
            const systemEl = document.getElementById('system-info');

            try {
                // Test 1: Supabase Configuration
                statusEl.textContent = '🔧 Testing Supabase configuration...';
                
                const supabaseUrl = 'https://hoyzvlfeyzzqbmhmsypy.supabase.co';
                const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhveXp2bGZleXp6cWJtaG1zeXB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDk4NDQsImV4cCI6MjA2NTA4NTg0NH0.0PTd0lfyPxXQmU1akMISJYU3eAcLPCZzrJxXrKnus0U';
                
                if (window.supabase) {
                    const supabaseClient = window.supabase.createClient(supabaseUrl, supabaseKey);
                    results.push('✅ Supabase client loaded successfully');
                    
                    // Test 2: Database Connection
                    statusEl.textContent = '🗄️ Testing database connection...';
                    try {
                        const { data, error } = await supabaseClient
                            .from('cv_templates')
                            .select('id')
                            .limit(1);
                        
                        if (error) {
                            results.push('❌ Database connection failed: ' + error.message);
                        } else {
                            results.push('✅ Database connection successful');
                        }
                    } catch (dbError) {
                        results.push('❌ Database test failed: ' + dbError.message);
                    }
                } else {
                    results.push('❌ Supabase client not loaded');
                }

                // Test 3: File Structure
                statusEl.textContent = '📁 Testing file structure...';
                const requiredFiles = [
                    'js/supabase-config.js',
                    'js/auth.js',
                    'css/style.css'
                ];

                for (const file of requiredFiles) {
                    try {
                        const response = await fetch(file, { method: 'HEAD' });
                        if (response.ok) {
                            results.push(`✅ ${file} - Available`);
                        } else {
                            results.push(`❌ ${file} - Not found (${response.status})`);
                        }
                    } catch (error) {
                        results.push(`❌ ${file} - Error: ${error.message}`);
                    }
                }

                // Test 4: Authentication Test
                statusEl.textContent = '🔐 Testing authentication system...';
                try {
                    if (window.supabase) {
                        const supabaseClient = window.supabase.createClient(supabaseUrl, supabaseKey);
                        const { data: { session } } = await supabaseClient.auth.getSession();
                        results.push('✅ Authentication system initialized');
                    } else {
                        results.push('❌ Authentication system not available');
                    }
                } catch (authError) {
                    results.push('❌ Authentication test failed: ' + authError.message);
                }

                // Final status
                const hasErrors = results.some(r => r.includes('❌'));
                if (hasErrors) {
                    statusEl.textContent = '⚠️ Deployment has some issues';
                    statusEl.className = 'status error';
                } else {
                    statusEl.textContent = '✅ Deployment successful!';
                    statusEl.className = 'status success';
                }

            } catch (error) {
                results.push('❌ Deployment test failed: ' + error.message);
                statusEl.textContent = '❌ Deployment test failed';
                statusEl.className = 'status error';
            }

            // Display results
            resultsEl.innerHTML = results.map(result => 
                `<div class="status ${result.includes('❌') ? 'error' : 'success'}">${result}</div>`
            ).join('');

            // Display system info
            const systemInfo = {
                'URL': window.location.href,
                'User Agent': navigator.userAgent,
                'Screen Size': `${screen.width}x${screen.height}`,
                'Viewport': `${window.innerWidth}x${window.innerHeight}`,
                'Language': navigator.language,
                'Platform': navigator.platform,
                'Online': navigator.onLine,
                'Timestamp': new Date().toISOString()
            };

            systemEl.innerHTML = `<pre>${JSON.stringify(systemInfo, null, 2)}</pre>`;
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', testDeployment);
    </script>
</body>
</html>
