name: Deploy Elashrafy CV to GitHub Pages

on:
  push:
    branches: [ main, master ]
  workflow_dispatch:

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Verify Supabase Configuration
      run: |
        echo "🔧 Checking Supabase configuration..."
        if grep -q "hoyzvlfeyzzqbmhmsypy" js/supabase-config.js; then
          echo "✅ Supabase URL configured correctly"
        else
          echo "❌ Supabase URL not found"
          exit 1
        fi

    - name: Setup Pages
      uses: actions/configure-pages@v4

    - name: Upload artifact
      uses: actions/upload-pages-artifact@v3
      with:
        path: '.'

    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v4
