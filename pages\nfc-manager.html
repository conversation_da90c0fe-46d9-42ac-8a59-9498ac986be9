<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة بطاقات NFC - Elashrafy CV</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/arabic.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .nfc-main {
            padding-top: 80px;
            min-height: 100vh;
            background: var(--gray-50);
        }
        
        .nfc-header {
            text-align: center;
            padding: var(--spacing-12) 0;
            background: linear-gradient(135deg, var(--success-color), #34d399);
            color: var(--white);
            margin-bottom: var(--spacing-12);
        }
        
        .nfc-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-8);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .nfc-form-section,
        .nfc-preview-section {
            background: var(--white);
            border-radius: var(--radius-xl);
            padding: var(--spacing-8);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
        }
        
        .section-title {
            font-size: var(--font-size-2xl);
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: var(--spacing-6);
            padding-bottom: var(--spacing-3);
            border-bottom: 2px solid var(--gray-100);
        }
        
        .nfc-status {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            padding: var(--spacing-3);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-6);
            font-weight: 500;
        }
        
        .nfc-status.supported {
            background: var(--success-color);
            background-opacity: 0.1;
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }
        
        .nfc-status.not-supported {
            background: var(--error-color);
            background-opacity: 0.1;
            color: var(--error-color);
            border: 1px solid var(--error-color);
        }
        
        .nfc-card-preview {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            border-radius: var(--radius-xl);
            padding: var(--spacing-6);
            color: var(--white);
            margin-bottom: var(--spacing-6);
            position: relative;
            overflow: hidden;
        }
        
        .nfc-card-preview::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .nfc-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-4);
        }
        
        .nfc-icon {
            font-size: var(--font-size-2xl);
        }
        
        .nfc-card-info h3 {
            margin: 0 0 var(--spacing-1) 0;
            font-size: var(--font-size-xl);
        }
        
        .nfc-card-info p {
            margin: 0;
            opacity: 0.9;
            font-size: var(--font-size-sm);
        }
        
        .nfc-actions {
            display: flex;
            gap: var(--spacing-3);
            margin-top: var(--spacing-6);
        }
        
        .nfc-actions .btn {
            flex: 1;
            justify-content: center;
        }
        
        .nfc-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: var(--spacing-6);
            margin-top: var(--spacing-12);
        }
        
        .nfc-card-item {
            background: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-6);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            transition: var(--transition);
        }
        
        .nfc-card-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        
        .nfc-card-status {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-1);
            padding: var(--spacing-1) var(--spacing-2);
            border-radius: var(--radius-full);
            font-size: var(--font-size-xs);
            font-weight: 500;
            margin-bottom: var(--spacing-3);
        }
        
        .nfc-card-status.active {
            background: var(--success-color);
            background-opacity: 0.1;
            color: var(--success-color);
        }
        
        .nfc-card-status.inactive {
            background: var(--gray-400);
            background-opacity: 0.1;
            color: var(--gray-600);
        }
        
        .nfc-instructions {
            background: var(--info-color);
            background-opacity: 0.1;
            border: 1px solid var(--info-color);
            border-radius: var(--radius-lg);
            padding: var(--spacing-4);
            margin-bottom: var(--spacing-6);
        }
        
        .nfc-instructions h4 {
            color: var(--info-color);
            margin-bottom: var(--spacing-2);
        }
        
        .nfc-instructions ol {
            margin: 0;
            padding-right: var(--spacing-4);
            color: var(--gray-700);
        }
        
        .nfc-instructions li {
            margin-bottom: var(--spacing-1);
        }
        
        @media (max-width: 768px) {
            .nfc-container {
                grid-template-columns: 1fr;
                gap: var(--spacing-6);
            }
            
            .nfc-actions {
                flex-direction: column;
            }
            
            .nfc-actions .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../index.html">
                        <i class="fas fa-file-alt"></i>
                        <span>Elashrafy CV</span>
                    </a>
                </div>
                
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="../index.html" class="nav-link">الرئيسية</a></li>
                        <li><a href="dashboard.html" class="nav-link">لوحة التحكم</a></li>
                        <li><a href="cv-builder.html" class="nav-link">منشئ السيرة الذاتية</a></li>
                        <li><a href="templates.html" class="nav-link">القوالب</a></li>
                        <li><a href="qr-generator.html" class="nav-link">مولد QR</a></li>
                        <li><a href="nfc-manager.html" class="nav-link active">إدارة NFC</a></li>
                    </ul>
                </nav>
                
                <div class="header-actions">
                    <div class="language-switcher">
                        <button class="language-btn" onclick="toggleLanguage()">
                            <i class="fas fa-globe"></i>
                            <span id="current-language">العربية</span>
                        </button>
                    </div>
                    
                    <div class="user-menu">
                        <button class="user-btn" id="user-menu-btn">
                            <i class="fas fa-user"></i>
                            <span id="user-name">المستخدم</span>
                        </button>
                        <div class="user-dropdown" id="user-dropdown">
                            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                        </div>
                    </div>
                    
                    <button class="nav-toggle" id="nav-toggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="nfc-main">
        <!-- Header Section -->
        <section class="nfc-header">
            <div class="container">
                <h1><i class="fas fa-credit-card"></i> إدارة بطاقات NFC</h1>
                <p>اربط سيرتك الذاتية ببطاقات NFC للمشاركة السريعة والذكية</p>
            </div>
        </section>

        <div class="container">
            <!-- NFC Support Status -->
            <div class="nfc-status" id="nfc-status">
                <i class="fas fa-spinner fa-spin"></i>
                <span>جاري فحص دعم NFC...</span>
            </div>

            <!-- NFC Manager -->
            <div class="nfc-container">
                <!-- Form Section -->
                <div class="nfc-form-section">
                    <h2 class="section-title">
                        <i class="fas fa-plus-circle"></i>
                        إنشاء بطاقة NFC جديدة
                    </h2>
                    
                    <!-- CV Selector -->
                    <div class="form-group">
                        <label>اختر السيرة الذاتية</label>
                        <select id="cv-selector">
                            <option value="">جاري تحميل السير الذاتية...</option>
                        </select>
                    </div>
                    
                    <!-- Card Name -->
                    <div class="form-group">
                        <label>اسم البطاقة</label>
                        <input type="text" id="card-name" placeholder="مثال: بطاقتي المهنية">
                        <div class="form-help">
                            <i class="fas fa-info-circle"></i>
                            اسم تعريفي للبطاقة لسهولة الإدارة
                        </div>
                    </div>
                    
                    <!-- Card Description -->
                    <div class="form-group">
                        <label>وصف البطاقة (اختياري)</label>
                        <textarea id="card-description" rows="3" placeholder="وصف مختصر للبطاقة..."></textarea>
                    </div>
                    
                    <!-- Instructions -->
                    <div class="nfc-instructions">
                        <h4><i class="fas fa-lightbulb"></i> كيفية استخدام بطاقات NFC</h4>
                        <ol>
                            <li>اختر السيرة الذاتية التي تريد ربطها</li>
                            <li>أدخل اسم البطاقة</li>
                            <li>انقر على "إنشاء بطاقة NFC"</li>
                            <li>اقترب بهاتفك من البطاقة لبرمجتها</li>
                            <li>شارك البطاقة مع الآخرين للوصول السريع لسيرتك</li>
                        </ol>
                    </div>
                    
                    <button class="btn btn-primary btn-full" onclick="createNFCCard()" id="create-nfc-btn">
                        <i class="fas fa-magic"></i>
                        إنشاء بطاقة NFC
                    </button>
                </div>
                
                <!-- Preview Section -->
                <div class="nfc-preview-section">
                    <h2 class="section-title">
                        <i class="fas fa-eye"></i>
                        معاينة البطاقة
                    </h2>
                    
                    <div class="nfc-card-preview" id="nfc-preview">
                        <div class="nfc-card-header">
                            <div class="nfc-icon">
                                <i class="fas fa-wifi"></i>
                            </div>
                            <div class="nfc-card-info">
                                <h3 id="preview-card-name">اسم البطاقة</h3>
                                <p id="preview-cv-title">عنوان السيرة الذاتية</p>
                            </div>
                        </div>
                        <p id="preview-description">وصف البطاقة سيظهر هنا</p>
                    </div>
                    
                    <div class="nfc-actions" id="nfc-actions" style="display: none;">
                        <button class="btn btn-outline" onclick="writeToNFC()">
                            <i class="fas fa-edit"></i>
                            برمجة البطاقة
                        </button>
                        <button class="btn btn-primary" onclick="saveNFCCard()">
                            <i class="fas fa-save"></i>
                            حفظ البطاقة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Saved NFC Cards -->
            <section class="saved-nfc-cards">
                <h2 class="section-title">
                    <i class="fas fa-archive"></i>
                    بطاقات NFC المحفوظة
                </h2>
                <div class="nfc-cards-grid" id="saved-nfc-cards">
                    <!-- Saved NFC cards will be loaded here -->
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>جاري تحميل بطاقات NFC...</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-file-alt"></i>
                        <span>Elashrafy CV</span>
                    </div>
                    <p>منصة شاملة لإنشاء وإدارة السيرة الذاتية بتقنيات حديثة</p>
                </div>
                
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="../index.html">الرئيسية</a></li>
                        <li><a href="cv-builder.html">منشئ السيرة الذاتية</a></li>
                        <li><a href="templates.html">القوالب</a></li>
                        <li><a href="dashboard.html">لوحة التحكم</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>الدعم</h4>
                    <ul>
                        <li><a href="#help">المساعدة</a></li>
                        <li><a href="#contact">اتصل بنا</a></li>
                        <li><a href="#privacy">سياسة الخصوصية</a></li>
                        <li><a href="#terms">شروط الاستخدام</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>تابعنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Elashrafy CV. جميع الحقوق محفوظة.</p>
                <p>تم التطوير بواسطة <a href="#" class="footer-credit">Elashrafy</a></p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../js/supabase-config.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/db-service.js"></script>
    <script src="../js/translations.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        // NFC Manager functionality
        class NFCManager {
            constructor() {
                this.isNFCSupported = false;
                this.userCVs = [];
                this.savedNFCCards = [];
                this.currentCard = null;
                this.init();
            }

            async init() {
                // Check authentication
                if (!window.authManager || !window.authManager.isAuthenticated()) {
                    window.location.href = '../index.html';
                    return;
                }

                await this.checkNFCSupport();
                await this.loadUserCVs();
                await this.loadSavedNFCCards();
                this.setupEventListeners();
            }

            async checkNFCSupport() {
                const statusEl = document.getElementById('nfc-status');
                
                if ('NDEFReader' in window) {
                    this.isNFCSupported = true;
                    statusEl.className = 'nfc-status supported';
                    statusEl.innerHTML = `
                        <i class="fas fa-check-circle"></i>
                        <span>جهازك يدعم تقنية NFC</span>
                    `;
                } else {
                    this.isNFCSupported = false;
                    statusEl.className = 'nfc-status not-supported';
                    statusEl.innerHTML = `
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>جهازك لا يدعم تقنية NFC أو المتصفح لا يدعم Web NFC API</span>
                    `;
                }
            }

            async loadUserCVs() {
                try {
                    const user = window.authManager.getCurrentUser();
                    const { data, error } = await window.dbService.getUserCVs(user.id);

                    if (error) throw error;

                    this.userCVs = data || [];
                    this.renderCVSelector();
                } catch (error) {
                    console.error('Error loading CVs:', error);
                    this.showMessage('فشل في تحميل السير الذاتية', 'error');
                }
            }

            renderCVSelector() {
                const selector = document.getElementById('cv-selector');

                if (this.userCVs.length === 0) {
                    selector.innerHTML = '<option value="">لا توجد سير ذاتية متاحة</option>';
                    return;
                }

                selector.innerHTML = `
                    <option value="">اختر سيرة ذاتية</option>
                    ${this.userCVs.map(cv => `
                        <option value="${cv.id}">${cv.title}</option>
                    `).join('')}
                `;
            }

            setupEventListeners() {
                // CV selector change
                document.getElementById('cv-selector').addEventListener('change', (e) => {
                    this.updatePreview();
                });

                // Card name input
                document.getElementById('card-name').addEventListener('input', (e) => {
                    this.updatePreview();
                });

                // Card description input
                document.getElementById('card-description').addEventListener('input', (e) => {
                    this.updatePreview();
                });
            }

            updatePreview() {
                const cvId = document.getElementById('cv-selector').value;
                const cardName = document.getElementById('card-name').value;
                const description = document.getElementById('card-description').value;

                const cv = this.userCVs.find(cv => cv.id === cvId);

                document.getElementById('preview-card-name').textContent = cardName || 'اسم البطاقة';
                document.getElementById('preview-cv-title').textContent = cv ? cv.title : 'عنوان السيرة الذاتية';
                document.getElementById('preview-description').textContent = description || 'وصف البطاقة سيظهر هنا';

                // Show/hide actions based on form completion
                const actionsEl = document.getElementById('nfc-actions');
                if (cvId && cardName) {
                    actionsEl.style.display = 'flex';
                    this.currentCard = {
                        cvId: cvId,
                        cardName: cardName,
                        description: description,
                        url: `${window.location.origin}/cv-view.html?id=${cvId}`
                    };
                } else {
                    actionsEl.style.display = 'none';
                    this.currentCard = null;
                }
            }

            async writeToNFC() {
                if (!this.isNFCSupported) {
                    this.showMessage('جهازك لا يدعم تقنية NFC', 'error');
                    return;
                }

                if (!this.currentCard) {
                    this.showMessage('يرجى إكمال بيانات البطاقة أولاً', 'warning');
                    return;
                }

                try {
                    const ndef = new NDEFReader();

                    this.showMessage('اقترب بجهازك من بطاقة NFC...', 'info');

                    await ndef.write({
                        records: [
                            {
                                recordType: "url",
                                data: this.currentCard.url
                            },
                            {
                                recordType: "text",
                                data: this.currentCard.cardName
                            }
                        ]
                    });

                    this.showMessage('تم برمجة البطاقة بنجاح!', 'success');

                } catch (error) {
                    console.error('Error writing to NFC:', error);
                    if (error.name === 'NotAllowedError') {
                        this.showMessage('تم رفض الإذن للوصول إلى NFC', 'error');
                    } else if (error.name === 'NotSupportedError') {
                        this.showMessage('جهازك لا يدعم كتابة NFC', 'error');
                    } else {
                        this.showMessage('فشل في برمجة البطاقة', 'error');
                    }
                }
            }

            async saveNFCCard() {
                if (!this.currentCard) {
                    this.showMessage('يرجى إكمال بيانات البطاقة أولاً', 'warning');
                    return;
                }

                try {
                    const user = window.authManager.getCurrentUser();

                    const { error } = await window.dbService.createNFCCard({
                        user_id: user.id,
                        cv_id: this.currentCard.cvId,
                        card_name: this.currentCard.cardName,
                        description: this.currentCard.description,
                        nfc_url: this.currentCard.url,
                        is_active: true
                    });

                    if (error) throw error;

                    this.showMessage('تم حفظ بطاقة NFC بنجاح!', 'success');
                    await this.loadSavedNFCCards();

                    // Clear form
                    document.getElementById('cv-selector').value = '';
                    document.getElementById('card-name').value = '';
                    document.getElementById('card-description').value = '';
                    this.updatePreview();

                } catch (error) {
                    console.error('Error saving NFC card:', error);
                    this.showMessage('فشل في حفظ بطاقة NFC', 'error');
                }
            }

            async loadSavedNFCCards() {
                try {
                    const user = window.authManager.getCurrentUser();
                    const { data, error } = await window.dbService.getUserNFCCards(user.id);

                    if (error) throw error;

                    this.savedNFCCards = data || [];
                    this.renderSavedNFCCards();
                } catch (error) {
                    console.error('Error loading saved NFC cards:', error);
                }
            }

            renderSavedNFCCards() {
                const container = document.getElementById('saved-nfc-cards');

                if (this.savedNFCCards.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-credit-card"></i>
                            <p>لا توجد بطاقات NFC محفوظة</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = this.savedNFCCards.map(card => `
                    <div class="nfc-card-item">
                        <div class="nfc-card-status ${card.is_active ? 'active' : 'inactive'}">
                            <i class="fas fa-circle"></i>
                            ${card.is_active ? 'نشطة' : 'غير نشطة'}
                        </div>
                        <h4>${card.card_name}</h4>
                        <p><strong>السيرة الذاتية:</strong> ${card.cvs?.title || 'غير محدد'}</p>
                        <p><strong>تاريخ الإنشاء:</strong> ${new Date(card.created_at).toLocaleDateString('ar-SA')}</p>
                        ${card.description ? `<p><strong>الوصف:</strong> ${card.description}</p>` : ''}
                        <div class="nfc-card-actions">
                            <button class="btn btn-sm btn-outline" onclick="toggleNFCCard('${card.id}', ${!card.is_active})">
                                <i class="fas fa-power-off"></i>
                                ${card.is_active ? 'إيقاف' : 'تفعيل'}
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="editNFCCard('${card.id}')">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="deleteNFCCard('${card.id}')">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                `).join('');
            }

            async toggleNFCCard(cardId, isActive) {
                try {
                    const { error } = await window.dbService.updateNFCCard(cardId, {
                        is_active: isActive
                    });

                    if (error) throw error;

                    this.showMessage(`تم ${isActive ? 'تفعيل' : 'إيقاف'} البطاقة بنجاح!`, 'success');
                    await this.loadSavedNFCCards();
                } catch (error) {
                    console.error('Error toggling NFC card:', error);
                    this.showMessage('فشل في تحديث حالة البطاقة', 'error');
                }
            }

            async deleteNFCCard(cardId) {
                if (!confirm('هل أنت متأكد من حذف هذه البطاقة؟')) {
                    return;
                }

                try {
                    const { error } = await window.dbService.deleteNFCCard(cardId);
                    if (error) throw error;

                    this.showMessage('تم حذف البطاقة بنجاح!', 'success');
                    await this.loadSavedNFCCards();
                } catch (error) {
                    console.error('Error deleting NFC card:', error);
                    this.showMessage('فشل في حذف البطاقة', 'error');
                }
            }

            showMessage(message, type = 'info') {
                if (window.authManager) {
                    window.authManager.showMessage(message, type);
                }
            }
        }

        // Global functions
        function createNFCCard() {
            window.nfcManager.updatePreview();
            if (window.nfcManager.currentCard) {
                window.nfcManager.showMessage('البطاقة جاهزة للبرمجة والحفظ', 'success');
            } else {
                window.nfcManager.showMessage('يرجى إكمال بيانات البطاقة', 'warning');
            }
        }

        function writeToNFC() {
            window.nfcManager.writeToNFC();
        }

        function saveNFCCard() {
            window.nfcManager.saveNFCCard();
        }

        function toggleNFCCard(cardId, isActive) {
            window.nfcManager.toggleNFCCard(cardId, isActive);
        }

        function editNFCCard(cardId) {
            // For now, just show a message - can be expanded later
            window.nfcManager.showMessage('ميزة التعديل ستكون متاحة قريباً', 'info');
        }

        function deleteNFCCard(cardId) {
            window.nfcManager.deleteNFCCard(cardId);
        }

        // Initialize NFC manager
        document.addEventListener('DOMContentLoaded', () => {
            window.nfcManager = new NFCManager();
        });
    </script>
</body>
</html>
