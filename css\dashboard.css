/* Dashboard Styles */

.dashboard-main {
    padding-top: calc(80px + var(--spacing-8));
    min-height: 100vh;
    background: var(--gray-50);
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-8);
    padding: var(--spacing-6) 0;
    border-bottom: 1px solid var(--gray-200);
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
}

.dashboard-title h1 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.dashboard-title p {
    color: var(--gray-600);
    font-size: var(--font-size-lg);
}

.dashboard-actions {
    display: flex;
    gap: var(--spacing-3);
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-12);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    transition: var(--transition);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xl);
    color: var(--white);
    flex-shrink: 0;
}

.stat-content h3 {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.stat-content p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: var(--spacing-12);
}

.quick-actions h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-6);
}

.action-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    text-decoration: none;
    color: inherit;
    border: 1px solid var(--gray-200);
    transition: var(--transition);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: var(--transition);
}

.action-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    color: inherit;
}

.action-card:hover::before {
    transform: scaleX(1);
}

.action-icon {
    width: 80px;
    height: 80px;
    background: var(--gray-100);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-4);
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    transition: var(--transition);
}

.action-card:hover .action-icon {
    background: var(--primary-color);
    color: var(--white);
}

.action-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.action-card p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

/* Recent Sections */
.recent-section {
    margin-bottom: var(--spacing-12);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
}

.section-header h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-900);
}

/* CVs Grid */
.cvs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-6);
}

.cv-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
}

.cv-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.cv-preview {
    height: 200px;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.cv-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color)20, var(--primary-light)20);
}

.cv-preview i {
    font-size: 3rem;
    color: var(--primary-color);
    z-index: 1;
}

.cv-info {
    padding: var(--spacing-4);
}

.cv-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.cv-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-3);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.cv-actions {
    display: flex;
    gap: var(--spacing-2);
}

.cv-actions .btn {
    flex: 1;
    font-size: var(--font-size-sm);
    padding: var(--spacing-2) var(--spacing-3);
}

/* QR Codes Grid */
.qr-codes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-6);
}

.qr-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-4);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    text-align: center;
    transition: var(--transition);
}

.qr-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.qr-preview {
    width: 120px;
    height: 120px;
    background: var(--gray-100);
    border-radius: var(--radius-lg);
    margin: 0 auto var(--spacing-3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
}

.qr-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.qr-meta {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--spacing-3);
}

.qr-actions {
    display: flex;
    gap: var(--spacing-2);
}

.qr-actions .btn {
    flex: 1;
    font-size: var(--font-size-sm);
    padding: var(--spacing-2) var(--spacing-3);
}

/* NFC Cards Grid */
.nfc-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--spacing-6);
}

.nfc-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--spacing-4);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
}

.nfc-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nfc-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-3);
}

.nfc-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-lg);
    color: var(--white);
}

.nfc-info h3 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.nfc-status {
    font-size: var(--font-size-sm);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-full);
    font-weight: 500;
}

.nfc-status.active {
    background: var(--success-color)20;
    color: var(--success-color);
}

.nfc-status.inactive {
    background: var(--gray-200);
    color: var(--gray-600);
}

.nfc-meta {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--spacing-3);
}

.nfc-actions {
    display: flex;
    gap: var(--spacing-2);
}

.nfc-actions .btn {
    flex: 1;
    font-size: var(--font-size-sm);
    padding: var(--spacing-2) var(--spacing-3);
}

/* Loading Placeholder */
.loading-placeholder {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-12);
    color: var(--gray-500);
}

.loading-placeholder i {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-3);
    color: var(--primary-color);
}

/* Empty State */
.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-12);
    color: var(--gray-500);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-4);
    color: var(--gray-300);
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    color: var(--gray-600);
    margin-bottom: var(--spacing-2);
}

.empty-state p {
    margin-bottom: var(--spacing-4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-main {
        padding-top: calc(80px + var(--spacing-4));
    }

    .dashboard-header {
        flex-direction: column;
        gap: var(--spacing-4);
        align-items: stretch;
        text-align: center;
    }

    .dashboard-title h1 {
        font-size: var(--font-size-2xl);
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .stat-card {
        padding: var(--spacing-4);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }

    .actions-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .action-icon {
        width: 60px;
        height: 60px;
        font-size: var(--font-size-xl);
    }

    .cvs-grid,
    .qr-codes-grid,
    .nfc-cards-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-4);
    }

    .section-header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;
    }

    .section-header .btn {
        align-self: center;
    }
}

@media (max-width: 480px) {
    .dashboard-header {
        padding: var(--spacing-4);
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-3);
    }

    .cv-actions,
    .qr-actions,
    .nfc-actions {
        flex-direction: column;
    }
}
