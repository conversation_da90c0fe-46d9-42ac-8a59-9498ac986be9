-- Complete Database Setup for Elashrafy CV
-- Execute this SQL in Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- QR codes policies
CREATE POLICY "Users can view own QR codes" ON qr_codes
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own QR codes" ON qr_codes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own QR codes" ON qr_codes
    FOR DELETE USING (auth.uid() = user_id);

-- NFC cards policies
CREATE POLICY "Users can view own NFC cards" ON nfc_cards
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own NFC cards" ON nfc_cards
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own NFC cards" ON nfc_cards
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own NFC cards" ON nfc_cards
    FOR DELETE USING (auth.uid() = user_id);

-- CV analytics policies
CREATE POLICY "Users can view analytics for own CVs" ON cv_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM cvs
            WHERE cvs.id = cv_analytics.cv_id
            AND cvs.user_id = auth.uid()
        )
    );

CREATE POLICY "Anyone can insert analytics" ON cv_analytics
    FOR INSERT WITH CHECK (true);

-- CV templates are public for reading
CREATE POLICY "Templates are public" ON cv_templates
    FOR SELECT USING (is_active = true);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at
    BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cvs_updated_at
    BEFORE UPDATE ON cvs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cv_templates_updated_at
    BEFORE UPDATE ON cv_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_nfc_cards_updated_at
    BEFORE UPDATE ON nfc_cards
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to handle user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to increment CV views
CREATE OR REPLACE FUNCTION increment_cv_views(cv_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE cvs SET views = views + 1 WHERE id = cv_uuid;

    INSERT INTO cv_analytics (cv_id, event_type, event_data)
    VALUES (cv_uuid, 'view', '{"timestamp": "' || NOW() || '"}');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user statistics
CREATE OR REPLACE FUNCTION get_user_stats(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_cvs', (SELECT COUNT(*) FROM cvs WHERE user_id = user_uuid),
        'total_views', (SELECT COALESCE(SUM(views), 0) FROM cvs WHERE user_id = user_uuid),
        'total_qr_codes', (SELECT COUNT(*) FROM qr_codes WHERE user_id = user_uuid),
        'total_nfc_cards', (SELECT COUNT(*) FROM nfc_cards WHERE user_id = user_uuid),
        'active_nfc_cards', (SELECT COUNT(*) FROM nfc_cards WHERE user_id = user_uuid AND is_active = true)
    ) INTO result;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION increment_cv_views(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_user_stats(UUID) TO authenticated;

-- Create a view for public CVs
CREATE OR REPLACE VIEW public_cvs AS
SELECT
    c.id,
    c.title,
    c.cv_data,
    c.views,
    c.created_at,
    c.updated_at,
    p.full_name as owner_name,
    p.avatar_url as owner_avatar,
    t.name as template_name
FROM cvs c
LEFT JOIN profiles p ON c.user_id = p.id
LEFT JOIN cv_templates t ON c.template_id = t.id
WHERE c.is_public = true;

-- Grant access to the view
GRANT SELECT ON public_cvs TO anon, authenticated;

-- Insert default CV templates
INSERT INTO cv_templates (name, name_ar, description, description_ar, category, template_data, is_active) VALUES
(
    'Modern Professional',
    'مهني حديث',
    'Clean and modern design perfect for tech professionals',
    'تصميم نظيف وحديث مثالي للمهنيين التقنيين',
    'modern',
    '{"colors": {"primary": "#2563eb", "secondary": "#64748b"}, "fonts": {"primary": "Cairo", "secondary": "Inter"}}',
    true
),
(
    'Classic Executive',
    'تنفيذي كلاسيكي',
    'Traditional layout ideal for executive positions',
    'تخطيط تقليدي مثالي للمناصب التنفيذية',
    'classic',
    '{"colors": {"primary": "#f59e0b", "secondary": "#6b7280"}, "fonts": {"primary": "Noto Sans Arabic", "secondary": "Georgia"}}',
    true
),
(
    'Creative Designer',
    'مصمم إبداعي',
    'Bold and creative design for creative professionals',
    'تصميم جريء وإبداعي للمهنيين المبدعين',
    'creative',
    '{"colors": {"primary": "#10b981", "secondary": "#8b5cf6"}, "fonts": {"primary": "Cairo", "secondary": "Poppins"}}',
    true
),
(
    'Minimal Clean',
    'بسيط نظيف',
    'Minimalist design focusing on content',
    'تصميم بسيط يركز على المحتوى',
    'minimal',
    '{"colors": {"primary": "#1f2937", "secondary": "#9ca3af"}, "fonts": {"primary": "Inter", "secondary": "Cairo"}}',
    true
),
(
    'Professional Corporate',
    'مؤسسي مهني',
    'Corporate style perfect for business professionals',
    'أسلوب مؤسسي مثالي للمهنيين في الأعمال',
    'professional',
    '{"colors": {"primary": "#1e40af", "secondary": "#475569"}, "fonts": {"primary": "Cairo", "secondary": "Roboto"}}',
    true
);
