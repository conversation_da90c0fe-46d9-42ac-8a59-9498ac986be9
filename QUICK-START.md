# 🚀 Quick Start - Main Application Authentication

## Step 1: Database Setup (2 minutes)

1. **Open Supabase Dashboard**
   - Go to: https://supabase.com/dashboard
   - Select project: `elashrafy-cv-builder`

2. **Execute Database Setup**
   - Go to **SQL Editor**
   - Click **"New Query"**
   - Copy and paste this SQL:

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for profiles
CREATE POLICY "Users can view own profile" ON profiles 
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles 
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for automatic profile creation
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
```

3. **Click "Run"** to execute the SQL

4. **Disable Email Confirmation (Optional)**
   - Go to **Authentication** > **Settings**
   - Under **"User Signups"**, uncheck **"Enable email confirmations"**
   - Click **"Save"**

## Step 2: Test Main Application (1 minute)

1. **Start Local Server**
   ```bash
   python -m http.server 8000
   ```

2. **Open Main Application**
   - Go to: http://localhost:8000
   - Wait for "✅ Ready" status in navigation

3. **Test Registration**
   - Click **"إنشاء حساب"** (Sign Up)
   - Fill form:
     - Name: `Test User`
     - Email: `<EMAIL>`
     - Password: `123456`
   - Click **"Sign Up"**

4. **Test Login**
   - Form should switch to login automatically
   - Email should be pre-filled
   - Enter password: `123456`
   - Click **"Login"**

## Expected Results

✅ **After successful login, you should see:**
- User menu in top navigation (instead of login/signup buttons)
- User name displayed
- "Go to Dashboard" button
- Access to protected pages

✅ **You can then access:**
- Dashboard: http://localhost:8000/pages/dashboard.html
- CV Builder: http://localhost:8000/pages/cv-builder.html

## Troubleshooting

### If "Connection failed" appears:
- Check SQL was executed correctly in Supabase
- Refresh the page

### If signup fails:
- Make sure email confirmation is disabled
- Try a different email address
- Check password is at least 6 characters

### If login fails:
- Use exact same credentials from signup
- Check browser console (F12) for errors

## That's It!

Your main application authentication is now working. You can:
1. Register new users through the main interface
2. Login existing users
3. Access protected features like dashboard and CV builder
4. All authentication happens through the main application UI

No need for separate test pages - everything works in the actual application interface!
