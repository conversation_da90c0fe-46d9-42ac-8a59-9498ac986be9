// Dashboard Management
class Dashboard {
    constructor() {
        this.currentUser = null;
        this.userCVs = [];
        this.userQRCodes = [];
        this.userNFCCards = [];
        this.init();
    }

    async init() {
        // Check authentication
        if (!window.authManager) {
            console.error('Auth manager not available');
            return;
        }

        // Wait for auth to be ready
        await this.waitForAuth();

        if (!window.authManager.isAuthenticated()) {
            window.authManager.showErrorMessage('Please login to access the dashboard');
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 2000);
            return;
        }

        this.currentUser = window.authManager.getCurrentUser();
        this.userProfile = window.authManager.getUserProfile();

        await this.loadDashboardData();
        this.setupEventListeners();
        this.updateUI();
    }

    async waitForAuth() {
        return new Promise((resolve) => {
            const checkAuth = () => {
                if (window.authManager && window.authManager.currentUser !== undefined) {
                    resolve();
                } else {
                    setTimeout(checkAuth, 100);
                }
            };
            checkAuth();
        });
    }

    async loadDashboardData() {
        try {
            // Load user data in parallel
            const [cvsResult, qrCodesResult, nfcCardsResult] = await Promise.all([
                window.dbService.getUserCVs(this.currentUser.id),
                window.dbService.getUserQRCodes(this.currentUser.id),
                window.dbService.getUserNFCCards(this.currentUser.id)
            ]);

            if (cvsResult.error) throw cvsResult.error;
            if (qrCodesResult.error) throw qrCodesResult.error;
            if (nfcCardsResult.error) throw nfcCardsResult.error;

            this.userCVs = cvsResult.data || [];
            this.userQRCodes = qrCodesResult.data || [];
            this.userNFCCards = nfcCardsResult.data || [];

            this.updateStats();
            this.displayRecentCVs();
            this.displayRecentQRCodes();
            this.displayRecentNFCCards();

        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.showError('Failed to load dashboard data');
        }
    }

    setupEventListeners() {
        // CV actions
        document.addEventListener('click', (e) => {
            if (e.target.matches('.cv-edit-btn')) {
                const cvId = e.target.dataset.cvId;
                this.editCV(cvId);
            } else if (e.target.matches('.cv-delete-btn')) {
                const cvId = e.target.dataset.cvId;
                this.deleteCV(cvId);
            } else if (e.target.matches('.cv-download-btn')) {
                const cvId = e.target.dataset.cvId;
                this.downloadCV(cvId);
            } else if (e.target.matches('.qr-download-btn')) {
                const qrId = e.target.dataset.qrId;
                this.downloadQRCode(qrId);
            } else if (e.target.matches('.qr-delete-btn')) {
                const qrId = e.target.dataset.qrId;
                this.deleteQRCode(qrId);
            } else if (e.target.matches('.nfc-edit-btn')) {
                const nfcId = e.target.dataset.nfcId;
                this.editNFCCard(nfcId);
            } else if (e.target.matches('.nfc-delete-btn')) {
                const nfcId = e.target.dataset.nfcId;
                this.deleteNFCCard(nfcId);
            }
        });

        // Real-time updates
        this.setupRealTimeUpdates();
    }

    setupRealTimeUpdates() {
        // Subscribe to real-time updates
        this.cvsSubscription = window.dbService.subscribeToUserCVs(
            this.currentUser.id,
            (payload) => {
                console.log('CVs updated:', payload);
                this.handleCVsUpdate(payload);
            }
        );

        this.qrCodesSubscription = window.dbService.subscribeToUserQRCodes(
            this.currentUser.id,
            (payload) => {
                console.log('QR codes updated:', payload);
                this.handleQRCodesUpdate(payload);
            }
        );

        this.nfcCardsSubscription = window.dbService.subscribeToUserNFCCards(
            this.currentUser.id,
            (payload) => {
                console.log('NFC cards updated:', payload);
                this.handleNFCCardsUpdate(payload);
            }
        );
    }

    handleCVsUpdate(payload) {
        // Reload CVs data
        this.loadUserCVs();
    }

    handleQRCodesUpdate(payload) {
        // Reload QR codes data
        this.loadUserQRCodes();
    }

    handleNFCCardsUpdate(payload) {
        // Reload NFC cards data
        this.loadUserNFCCards();
    }

    async loadUserCVs() {
        try {
            const { data, error } = await window.dbService.getUserCVs(this.currentUser.id);
            if (error) throw error;
            
            this.userCVs = data || [];
            this.updateStats();
            this.displayRecentCVs();
        } catch (error) {
            console.error('Error loading CVs:', error);
        }
    }

    async loadUserQRCodes() {
        try {
            const { data, error } = await window.dbService.getUserQRCodes(this.currentUser.id);
            if (error) throw error;
            
            this.userQRCodes = data || [];
            this.updateStats();
            this.displayRecentQRCodes();
        } catch (error) {
            console.error('Error loading QR codes:', error);
        }
    }

    async loadUserNFCCards() {
        try {
            const { data, error } = await window.dbService.getUserNFCCards(this.currentUser.id);
            if (error) throw error;
            
            this.userNFCCards = data || [];
            this.updateStats();
            this.displayRecentNFCCards();
        } catch (error) {
            console.error('Error loading NFC cards:', error);
        }
    }

    updateUI() {
        // Update welcome message
        const welcomeName = document.getElementById('user-welcome-name');
        if (welcomeName) {
            welcomeName.textContent = this.userProfile?.full_name || this.currentUser.email.split('@')[0];
        }
    }

    updateStats() {
        // Update stats cards
        const cvsCount = document.getElementById('cvs-count');
        const qrCodesCount = document.getElementById('qr-codes-count');
        const nfcCardsCount = document.getElementById('nfc-cards-count');
        const viewsCount = document.getElementById('views-count');

        if (cvsCount) cvsCount.textContent = this.userCVs.length;
        if (qrCodesCount) qrCodesCount.textContent = this.userQRCodes.length;
        if (nfcCardsCount) nfcCardsCount.textContent = this.userNFCCards.length;
        if (viewsCount) viewsCount.textContent = '0'; // Placeholder for future implementation
    }

    displayRecentCVs() {
        const container = document.getElementById('recent-cvs');
        if (!container) return;

        container.innerHTML = '';

        if (this.userCVs.length === 0) {
            container.innerHTML = this.createEmptyState(
                'fas fa-file-alt',
                'No CVs yet',
                'Create your first professional CV to get started.',
                'cv-builder.html',
                'Create CV'
            );
            return;
        }

        // Show recent 3 CVs
        const recentCVs = this.userCVs.slice(0, 3);
        recentCVs.forEach(cv => {
            const cvCard = this.createCVCard(cv);
            container.appendChild(cvCard);
        });
    }

    displayRecentQRCodes() {
        const container = document.getElementById('recent-qr-codes');
        if (!container) return;

        container.innerHTML = '';

        if (this.userQRCodes.length === 0) {
            container.innerHTML = this.createEmptyState(
                'fas fa-qrcode',
                'No QR codes yet',
                'Generate QR codes for your CVs to share them easily.',
                'qr-generator.html',
                'Generate QR Code'
            );
            return;
        }

        // Show recent 3 QR codes
        const recentQRCodes = this.userQRCodes.slice(0, 3);
        recentQRCodes.forEach(qr => {
            const qrCard = this.createQRCard(qr);
            container.appendChild(qrCard);
        });
    }

    displayRecentNFCCards() {
        const container = document.getElementById('recent-nfc-cards');
        if (!container) return;

        container.innerHTML = '';

        if (this.userNFCCards.length === 0) {
            container.innerHTML = this.createEmptyState(
                'fas fa-credit-card',
                'No NFC cards yet',
                'Create digital business cards for NFC-enabled devices.',
                'nfc-manager.html',
                'Create NFC Card'
            );
            return;
        }

        // Show recent 3 NFC cards
        const recentNFCCards = this.userNFCCards.slice(0, 3);
        recentNFCCards.forEach(nfc => {
            const nfcCard = this.createNFCCard(nfc);
            container.appendChild(nfcCard);
        });
    }

    createCVCard(cv) {
        const card = document.createElement('div');
        card.className = 'cv-card';
        card.innerHTML = `
            <div class="cv-preview">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="cv-info">
                <h3 class="cv-title">${cv.title}</h3>
                <div class="cv-meta">
                    <span><i class="fas fa-calendar"></i> ${this.formatDate(cv.updated_at)}</span>
                    <span class="cv-status ${cv.is_public ? 'public' : 'private'}">${cv.is_public ? 'Public' : 'Private'}</span>
                </div>
                <div class="cv-actions">
                    <button class="btn btn-outline cv-edit-btn" data-cv-id="${cv.id}">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-primary cv-download-btn" data-cv-id="${cv.id}">
                        <i class="fas fa-download"></i> Download
                    </button>
                    <button class="btn btn-outline cv-delete-btn" data-cv-id="${cv.id}" style="color: var(--error-color); border-color: var(--error-color);">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
        return card;
    }

    createQRCard(qr) {
        const card = document.createElement('div');
        card.className = 'qr-card';
        card.innerHTML = `
            <div class="qr-preview">
                <i class="fas fa-qrcode"></i>
            </div>
            <h3 class="qr-title">${qr.title}</h3>
            <div class="qr-meta">
                <span><i class="fas fa-calendar"></i> ${this.formatDate(qr.created_at)}</span>
            </div>
            <div class="qr-actions">
                <button class="btn btn-primary qr-download-btn" data-qr-id="${qr.id}">
                    <i class="fas fa-download"></i> Download
                </button>
                <button class="btn btn-outline qr-delete-btn" data-qr-id="${qr.id}" style="color: var(--error-color); border-color: var(--error-color);">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        return card;
    }

    createNFCCard(nfc) {
        const card = document.createElement('div');
        card.className = 'nfc-card';
        card.innerHTML = `
            <div class="nfc-header">
                <div class="nfc-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="nfc-info">
                    <h3>${nfc.card_name}</h3>
                    <span class="nfc-status ${nfc.is_active ? 'active' : 'inactive'}">
                        ${nfc.is_active ? 'Active' : 'Inactive'}
                    </span>
                </div>
            </div>
            <div class="nfc-meta">
                <span><i class="fas fa-calendar"></i> ${this.formatDate(nfc.updated_at)}</span>
            </div>
            <div class="nfc-actions">
                <button class="btn btn-outline nfc-edit-btn" data-nfc-id="${nfc.id}">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="btn btn-outline nfc-delete-btn" data-nfc-id="${nfc.id}" style="color: var(--error-color); border-color: var(--error-color);">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        return card;
    }

    createEmptyState(icon, title, description, link, buttonText) {
        return `
            <div class="empty-state">
                <i class="${icon}"></i>
                <h3>${title}</h3>
                <p>${description}</p>
                <a href="${link}" class="btn btn-primary">${buttonText}</a>
            </div>
        `;
    }

    // Action handlers
    editCV(cvId) {
        window.location.href = `cv-builder.html?id=${cvId}`;
    }

    async deleteCV(cvId) {
        if (!confirm('Are you sure you want to delete this CV?')) return;

        try {
            const { error } = await window.dbService.deleteCV(cvId);
            if (error) throw error;

            this.showSuccess('CV deleted successfully');
            await this.loadUserCVs();
        } catch (error) {
            console.error('Error deleting CV:', error);
            this.showError('Failed to delete CV');
        }
    }

    downloadCV(cvId) {
        // Implement CV download functionality
        this.showInfo('CV download feature coming soon!');
    }

    downloadQRCode(qrId) {
        // Implement QR code download functionality
        this.showInfo('QR code download feature coming soon!');
    }

    async deleteQRCode(qrId) {
        if (!confirm('Are you sure you want to delete this QR code?')) return;

        try {
            const { error } = await window.dbService.deleteQRCode(qrId);
            if (error) throw error;

            this.showSuccess('QR code deleted successfully');
            await this.loadUserQRCodes();
        } catch (error) {
            console.error('Error deleting QR code:', error);
            this.showError('Failed to delete QR code');
        }
    }

    editNFCCard(nfcId) {
        window.location.href = `nfc-manager.html?id=${nfcId}`;
    }

    async deleteNFCCard(nfcId) {
        if (!confirm('Are you sure you want to delete this NFC card?')) return;

        try {
            const { error } = await window.dbService.deleteNFCCard(nfcId);
            if (error) throw error;

            this.showSuccess('NFC card deleted successfully');
            await this.loadUserNFCCards();
        } catch (error) {
            console.error('Error deleting NFC card:', error);
            this.showError('Failed to delete NFC card');
        }
    }

    // Utility methods
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    }

    showSuccess(message) {
        if (window.authManager) {
            window.authManager.showSuccessMessage(message);
        }
    }

    showError(message) {
        if (window.authManager) {
            window.authManager.showErrorMessage(message);
        }
    }

    showInfo(message) {
        if (window.authManager) {
            window.authManager.showMessage(message, 'info');
        }
    }

    // Cleanup
    destroy() {
        if (this.cvsSubscription) {
            window.dbService.unsubscribe(this.cvsSubscription);
        }
        if (this.qrCodesSubscription) {
            window.dbService.unsubscribe(this.qrCodesSubscription);
        }
        if (this.nfcCardsSubscription) {
            window.dbService.unsubscribe(this.nfcCardsSubscription);
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.dashboard = new Dashboard();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.dashboard) {
        window.dashboard.destroy();
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Dashboard;
}
