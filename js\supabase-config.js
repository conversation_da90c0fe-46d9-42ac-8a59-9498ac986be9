// إعدادات Supabase للإنتاج
const SUPABASE_URL = 'https://hoyzvlfeyzzqbmhmsypy.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhveXp2bGZleXp6cWJtaG1zeXB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDk4NDQsImV4cCI6MjA2NTA4NTg0NH0.0PTd0lfyPxXQmU1akMISJYU3eAcLPCZzrJxXrKnus0U';

console.log('🔧 تحميل إعدادات Supabase...');

// Initialize Supabase client
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Database helper functions
class DatabaseService {
    constructor() {
        this.supabase = supabase;
    }

    // طرق المصادقة مع معالجة محسنة للأخطاء
    async signUp(email, password, fullName) {
        try {
            console.log('📝 محاولة إنشاء حساب جديد:', { email, fullName });

            // التحقق من صحة البيانات
            if (!email || !password || !fullName) {
                throw new Error('جميع الحقول مطلوبة');
            }

            if (password.length < 6) {
                throw new Error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            }

            if (!this.isValidEmail(email)) {
                throw new Error('يرجى إدخال بريد إلكتروني صحيح');
            }

            const { data, error } = await this.supabase.auth.signUp({
                email: email.toLowerCase().trim(),
                password,
                options: {
                    data: {
                        full_name: fullName.trim()
                    },
                    emailRedirectTo: window.location.origin
                }
            });

            if (error) {
                console.error('❌ خطأ في إنشاء الحساب:', error);
                throw error;
            }

            console.log('✅ تم إنشاء الحساب بنجاح:', data);
            return { data, error: null };
        } catch (error) {
            console.error('💥 خطأ في التسجيل:', error);
            return { data: null, error: this.formatAuthError(error) };
        }
    }

    async signIn(email, password) {
        try {
            console.log('🔑 محاولة تسجيل الدخول:', { email });

            // التحقق من صحة البيانات
            if (!email || !password) {
                throw new Error('البريد الإلكتروني وكلمة المرور مطلوبان');
            }

            if (!this.isValidEmail(email)) {
                throw new Error('يرجى إدخال بريد إلكتروني صحيح');
            }

            const { data, error } = await this.supabase.auth.signInWithPassword({
                email: email.toLowerCase().trim(),
                password
            });

            if (error) {
                console.error('❌ خطأ في تسجيل الدخول:', error);
                throw error;
            }

            console.log('✅ تم تسجيل الدخول بنجاح:', data);
            return { data, error: null };
        } catch (error) {
            console.error('💥 خطأ في تسجيل الدخول:', error);
            return { data: null, error: this.formatAuthError(error) };
        }
    }

    async signOut() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Sign out error:', error);
            return { error };
        }
    }

    async getCurrentUser() {
        try {
            const { data: { user }, error } = await this.supabase.auth.getUser();
            if (error) throw error;
            return { user, error: null };
        } catch (error) {
            console.error('Get user error:', error);
            return { user: null, error };
        }
    }

    // Profile methods
    async createProfile(userId, email, fullName) {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .insert([
                    {
                        id: userId,
                        email,
                        full_name: fullName,
                        avatar_url: null
                    }
                ]);

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Create profile error:', error);
            return { data: null, error };
        }
    }

    async getProfile(userId) {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .select('*')
                .eq('id', userId)
                .single();

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get profile error:', error);
            return { data: null, error };
        }
    }

    async updateProfile(userId, updates) {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .update(updates)
                .eq('id', userId);

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Update profile error:', error);
            return { data: null, error };
        }
    }

    // CV methods
    async createCV(userId, title, templateId, cvData) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .insert([
                    {
                        user_id: userId,
                        title,
                        template_id: templateId,
                        cv_data: cvData,
                        is_public: false
                    }
                ])
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Create CV error:', error);
            return { data: null, error };
        }
    }

    async getUserCVs(userId) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .select(`
                    *,
                    cv_templates (
                        name,
                        description,
                        preview_image
                    )
                `)
                .eq('user_id', userId)
                .order('updated_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get user CVs error:', error);
            return { data: null, error };
        }
    }

    async getCV(cvId) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .select(`
                    *,
                    cv_templates (
                        name,
                        description,
                        template_data
                    )
                `)
                .eq('id', cvId)
                .single();

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get CV error:', error);
            return { data: null, error };
        }
    }

    async updateCV(cvId, updates) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .update({
                    ...updates,
                    updated_at: new Date().toISOString()
                })
                .eq('id', cvId)
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Update CV error:', error);
            return { data: null, error };
        }
    }

    async deleteCV(cvId) {
        try {
            const { error } = await this.supabase
                .from('cvs')
                .delete()
                .eq('id', cvId);

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Delete CV error:', error);
            return { error };
        }
    }

    // Template methods
    async getTemplates() {
        try {
            const { data, error } = await this.supabase
                .from('cv_templates')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get templates error:', error);
            return { data: null, error };
        }
    }

    async getTemplate(templateId) {
        try {
            const { data, error } = await this.supabase
                .from('cv_templates')
                .select('*')
                .eq('id', templateId)
                .single();

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get template error:', error);
            return { data: null, error };
        }
    }

    // QR Code methods
    async createQRCode(userId, cvId, qrData, title) {
        try {
            const { data, error } = await this.supabase
                .from('qr_codes')
                .insert([
                    {
                        user_id: userId,
                        cv_id: cvId,
                        qr_data: qrData,
                        title: title || 'CV QR Code'
                    }
                ])
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Create QR code error:', error);
            return { data: null, error };
        }
    }

    async getUserQRCodes(userId) {
        try {
            const { data, error } = await this.supabase
                .from('qr_codes')
                .select(`
                    *,
                    cvs (
                        title
                    )
                `)
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get user QR codes error:', error);
            return { data: null, error };
        }
    }

    async deleteQRCode(qrCodeId) {
        try {
            const { error } = await this.supabase
                .from('qr_codes')
                .delete()
                .eq('id', qrCodeId);

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Delete QR code error:', error);
            return { error };
        }
    }

    // NFC Card methods
    async createNFCCard(userId, cvId, cardName, nfcData) {
        try {
            const { data, error } = await this.supabase
                .from('nfc_cards')
                .insert([
                    {
                        user_id: userId,
                        cv_id: cvId,
                        card_name: cardName,
                        nfc_data: nfcData,
                        is_active: true
                    }
                ])
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Create NFC card error:', error);
            return { data: null, error };
        }
    }

    async getUserNFCCards(userId) {
        try {
            const { data, error } = await this.supabase
                .from('nfc_cards')
                .select(`
                    *,
                    cvs (
                        title
                    )
                `)
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get user NFC cards error:', error);
            return { data: null, error };
        }
    }

    async updateNFCCard(cardId, updates) {
        try {
            const { data, error } = await this.supabase
                .from('nfc_cards')
                .update({
                    ...updates,
                    updated_at: new Date().toISOString()
                })
                .eq('id', cardId)
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Update NFC card error:', error);
            return { data: null, error };
        }
    }

    async deleteNFCCard(cardId) {
        try {
            const { error } = await this.supabase
                .from('nfc_cards')
                .delete()
                .eq('id', cardId);

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Delete NFC card error:', error);
            return { error };
        }
    }

    // Real-time subscriptions
    subscribeToUserCVs(userId, callback) {
        return this.supabase
            .channel('user-cvs')
            .on('postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'cvs',
                    filter: `user_id=eq.${userId}`
                },
                callback
            )
            .subscribe();
    }

    subscribeToUserQRCodes(userId, callback) {
        return this.supabase
            .channel('user-qr-codes')
            .on('postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'qr_codes',
                    filter: `user_id=eq.${userId}`
                },
                callback
            )
            .subscribe();
    }

    subscribeToUserNFCCards(userId, callback) {
        return this.supabase
            .channel('user-nfc-cards')
            .on('postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'nfc_cards',
                    filter: `user_id=eq.${userId}`
                },
                callback
            )
            .subscribe();
    }

    // Unsubscribe from channel
    unsubscribe(subscription) {
        if (subscription) {
            this.supabase.removeChannel(subscription);
        }
    }

    // Utility methods for validation and error handling
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    formatAuthError(error) {
        if (!error) return null;

        // ترجمة رسائل الخطأ الشائعة إلى العربية
        const errorMessages = {
            'Invalid login credentials': 'البريد الإلكتروني أو كلمة المرور غير صحيحة. يرجى التحقق من البيانات والمحاولة مرة أخرى.',
            'Email not confirmed': 'يرجى التحقق من بريدك الإلكتروني والنقر على رابط التأكيد قبل تسجيل الدخول.',
            'User already registered': 'يوجد حساب بهذا البريد الإلكتروني بالفعل. يرجى تسجيل الدخول بدلاً من ذلك.',
            'Password should be at least 6 characters': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل.',
            'Invalid email': 'يرجى إدخال بريد إلكتروني صحيح.',
            'Signup disabled': 'تسجيل الحسابات الجديدة معطل حالياً.',
            'Email rate limit exceeded': 'تم إرسال عدد كبير من الرسائل. يرجى الانتظار قبل طلب رسالة أخرى.',
            'Too many requests': 'محاولات تسجيل دخول كثيرة. يرجى الانتظار قليلاً قبل المحاولة مرة أخرى.',
            'جميع الحقول مطلوبة': 'جميع الحقول مطلوبة',
            'كلمة المرور يجب أن تكون 6 أحرف على الأقل': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
            'يرجى إدخال بريد إلكتروني صحيح': 'يرجى إدخال بريد إلكتروني صحيح',
            'البريد الإلكتروني وكلمة المرور مطلوبان': 'البريد الإلكتروني وكلمة المرور مطلوبان'
        };

        const message = error.message || error;
        return errorMessages[message] || message;
    }

    // Connection and health check methods
    async checkConnection() {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .select('id')
                .limit(1);

            return { connected: !error, error };
        } catch (error) {
            return { connected: false, error };
        }
    }

    async getServerHealth() {
        try {
            const startTime = Date.now();
            await this.checkConnection();
            const responseTime = Date.now() - startTime;

            return {
                status: 'healthy',
                responseTime: `${responseTime}ms`,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
}

// Create global database service instance
window.dbService = new DatabaseService();

// Ensure the service is available globally
console.log('✅ DatabaseService initialized:', !!window.dbService);

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DatabaseService, supabase };
}
