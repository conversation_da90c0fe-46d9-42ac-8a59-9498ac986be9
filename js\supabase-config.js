// Supabase Configuration
const SUPABASE_URL = 'https://hoyzvlfeyzzqbmhmsypy.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhveXp2bGZleXp6cWJtaG1zeXB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDk4NDQsImV4cCI6MjA2NTA4NTg0NH0.0PTd0lfyPxXQmU1akMISJYU3eAcLPCZzrJxXrKnus0U';

// Initialize Supabase client
const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Database helper functions
class DatabaseService {
    constructor() {
        this.supabase = supabase;
    }

    // Authentication methods with enhanced error handling
    async signUp(email, password, fullName) {
        try {
            // Validate input
            if (!email || !password || !fullName) {
                throw new Error('All fields are required');
            }

            if (password.length < 6) {
                throw new Error('Password must be at least 6 characters long');
            }

            if (!this.isValidEmail(email)) {
                throw new Error('Please enter a valid email address');
            }

            const { data, error } = await this.supabase.auth.signUp({
                email: email.toLowerCase().trim(),
                password,
                options: {
                    data: {
                        full_name: fullName.trim()
                    }
                }
            });

            if (error) throw error;

            // Profile will be created automatically by the database trigger
            return { data, error: null };
        } catch (error) {
            console.error('Sign up error:', error);
            return { data: null, error: this.formatAuthError(error) };
        }
    }

    async signIn(email, password) {
        try {
            // Validate input
            if (!email || !password) {
                throw new Error('Email and password are required');
            }

            if (!this.isValidEmail(email)) {
                throw new Error('Please enter a valid email address');
            }

            const { data, error } = await this.supabase.auth.signInWithPassword({
                email: email.toLowerCase().trim(),
                password
            });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Sign in error:', error);
            return { data: null, error: this.formatAuthError(error) };
        }
    }

    async signOut() {
        try {
            const { error } = await this.supabase.auth.signOut();
            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Sign out error:', error);
            return { error };
        }
    }

    async getCurrentUser() {
        try {
            const { data: { user }, error } = await this.supabase.auth.getUser();
            if (error) throw error;
            return { user, error: null };
        } catch (error) {
            console.error('Get user error:', error);
            return { user: null, error };
        }
    }

    // Profile methods
    async createProfile(userId, email, fullName) {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .insert([
                    {
                        id: userId,
                        email,
                        full_name: fullName,
                        avatar_url: null
                    }
                ]);

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Create profile error:', error);
            return { data: null, error };
        }
    }

    async getProfile(userId) {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .select('*')
                .eq('id', userId)
                .single();

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get profile error:', error);
            return { data: null, error };
        }
    }

    async updateProfile(userId, updates) {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .update(updates)
                .eq('id', userId);

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Update profile error:', error);
            return { data: null, error };
        }
    }

    // CV methods
    async createCV(userId, title, templateId, cvData) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .insert([
                    {
                        user_id: userId,
                        title,
                        template_id: templateId,
                        cv_data: cvData,
                        is_public: false
                    }
                ])
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Create CV error:', error);
            return { data: null, error };
        }
    }

    async getUserCVs(userId) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .select(`
                    *,
                    cv_templates (
                        name,
                        description,
                        preview_image
                    )
                `)
                .eq('user_id', userId)
                .order('updated_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get user CVs error:', error);
            return { data: null, error };
        }
    }

    async getCV(cvId) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .select(`
                    *,
                    cv_templates (
                        name,
                        description,
                        template_data
                    )
                `)
                .eq('id', cvId)
                .single();

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get CV error:', error);
            return { data: null, error };
        }
    }

    async updateCV(cvId, updates) {
        try {
            const { data, error } = await this.supabase
                .from('cvs')
                .update({
                    ...updates,
                    updated_at: new Date().toISOString()
                })
                .eq('id', cvId)
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Update CV error:', error);
            return { data: null, error };
        }
    }

    async deleteCV(cvId) {
        try {
            const { error } = await this.supabase
                .from('cvs')
                .delete()
                .eq('id', cvId);

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Delete CV error:', error);
            return { error };
        }
    }

    // Template methods
    async getTemplates() {
        try {
            const { data, error } = await this.supabase
                .from('cv_templates')
                .select('*')
                .order('created_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get templates error:', error);
            return { data: null, error };
        }
    }

    async getTemplate(templateId) {
        try {
            const { data, error } = await this.supabase
                .from('cv_templates')
                .select('*')
                .eq('id', templateId)
                .single();

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get template error:', error);
            return { data: null, error };
        }
    }

    // QR Code methods
    async createQRCode(userId, cvId, qrData, title) {
        try {
            const { data, error } = await this.supabase
                .from('qr_codes')
                .insert([
                    {
                        user_id: userId,
                        cv_id: cvId,
                        qr_data: qrData,
                        title: title || 'CV QR Code'
                    }
                ])
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Create QR code error:', error);
            return { data: null, error };
        }
    }

    async getUserQRCodes(userId) {
        try {
            const { data, error } = await this.supabase
                .from('qr_codes')
                .select(`
                    *,
                    cvs (
                        title
                    )
                `)
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get user QR codes error:', error);
            return { data: null, error };
        }
    }

    async deleteQRCode(qrCodeId) {
        try {
            const { error } = await this.supabase
                .from('qr_codes')
                .delete()
                .eq('id', qrCodeId);

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Delete QR code error:', error);
            return { error };
        }
    }

    // NFC Card methods
    async createNFCCard(userId, cvId, cardName, nfcData) {
        try {
            const { data, error } = await this.supabase
                .from('nfc_cards')
                .insert([
                    {
                        user_id: userId,
                        cv_id: cvId,
                        card_name: cardName,
                        nfc_data: nfcData,
                        is_active: true
                    }
                ])
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Create NFC card error:', error);
            return { data: null, error };
        }
    }

    async getUserNFCCards(userId) {
        try {
            const { data, error } = await this.supabase
                .from('nfc_cards')
                .select(`
                    *,
                    cvs (
                        title
                    )
                `)
                .eq('user_id', userId)
                .order('created_at', { ascending: false });

            if (error) throw error;
            return { data, error: null };
        } catch (error) {
            console.error('Get user NFC cards error:', error);
            return { data: null, error };
        }
    }

    async updateNFCCard(cardId, updates) {
        try {
            const { data, error } = await this.supabase
                .from('nfc_cards')
                .update({
                    ...updates,
                    updated_at: new Date().toISOString()
                })
                .eq('id', cardId)
                .select();

            if (error) throw error;
            return { data: data[0], error: null };
        } catch (error) {
            console.error('Update NFC card error:', error);
            return { data: null, error };
        }
    }

    async deleteNFCCard(cardId) {
        try {
            const { error } = await this.supabase
                .from('nfc_cards')
                .delete()
                .eq('id', cardId);

            if (error) throw error;
            return { error: null };
        } catch (error) {
            console.error('Delete NFC card error:', error);
            return { error };
        }
    }

    // Real-time subscriptions
    subscribeToUserCVs(userId, callback) {
        return this.supabase
            .channel('user-cvs')
            .on('postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'cvs',
                    filter: `user_id=eq.${userId}`
                },
                callback
            )
            .subscribe();
    }

    subscribeToUserQRCodes(userId, callback) {
        return this.supabase
            .channel('user-qr-codes')
            .on('postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'qr_codes',
                    filter: `user_id=eq.${userId}`
                },
                callback
            )
            .subscribe();
    }

    subscribeToUserNFCCards(userId, callback) {
        return this.supabase
            .channel('user-nfc-cards')
            .on('postgres_changes',
                {
                    event: '*',
                    schema: 'public',
                    table: 'nfc_cards',
                    filter: `user_id=eq.${userId}`
                },
                callback
            )
            .subscribe();
    }

    // Unsubscribe from channel
    unsubscribe(subscription) {
        if (subscription) {
            this.supabase.removeChannel(subscription);
        }
    }

    // Utility methods for validation and error handling
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    formatAuthError(error) {
        if (!error) return null;

        // Map common Supabase auth errors to user-friendly messages
        const errorMessages = {
            'Invalid login credentials': 'Invalid email or password. Please check your credentials and try again.',
            'Email not confirmed': 'Please check your email and click the confirmation link before signing in.',
            'User already registered': 'An account with this email already exists. Please sign in instead.',
            'Password should be at least 6 characters': 'Password must be at least 6 characters long.',
            'Invalid email': 'Please enter a valid email address.',
            'Signup disabled': 'New account registration is currently disabled.',
            'Email rate limit exceeded': 'Too many emails sent. Please wait before requesting another.',
            'Too many requests': 'Too many login attempts. Please wait a moment before trying again.'
        };

        const message = error.message || error;
        return errorMessages[message] || message;
    }

    // Connection and health check methods
    async checkConnection() {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .select('id')
                .limit(1);

            return { connected: !error, error };
        } catch (error) {
            return { connected: false, error };
        }
    }

    async getServerHealth() {
        try {
            const startTime = Date.now();
            await this.checkConnection();
            const responseTime = Date.now() - startTime;

            return {
                status: 'healthy',
                responseTime: `${responseTime}ms`,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
}

// Create global database service instance
window.dbService = new DatabaseService();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DatabaseService, supabase };
}
