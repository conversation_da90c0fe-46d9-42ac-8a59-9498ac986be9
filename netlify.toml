# Netlify configuration for Elashrafy CV

[build]
  # Build command (if needed)
  command = "echo 'No build step required for static site'"
  
  # Directory to publish
  publish = "."

[build.environment]
  # Environment variables for build (if needed)
  NODE_VERSION = "18"

# Redirect rules
[[redirects]]
  # Redirect root to index.html
  from = "/"
  to = "/index.html"
  status = 200

[[redirects]]
  # Handle SPA routing for pages
  from = "/pages/*"
  to = "/pages/:splat"
  status = 200

[[redirects]]
  # Fallback for any other routes
  from = "/*"
  to = "/index.html"
  status = 404

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Performance headers
    Cache-Control = "public, max-age=********"

[[headers]]
  for = "*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.jpg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.png"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

[[headers]]
  for = "*.svg"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Form handling (if needed for contact forms)
# [[forms]]
#   name = "contact"

# Functions (if using Netlify Functions)
# [functions]
#   directory = "netlify/functions"
