<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار منشئ السيرة الذاتية العربي</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/cv-builder.css">
    <link rel="stylesheet" href="css/arabic.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts - Arabic Support -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            direction: rtl;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            color: #2563eb;
            font-family: 'Cairo', Arial, sans-serif;
            font-size: 1.5rem;
            margin-bottom: 1rem;
            text-align: right;
        }
        
        .demo-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .demo-preview {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            min-height: 400px;
            background: #f9fafb;
        }
        
        .feature-demo {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
        }
        
        .feature-demo > div {
            flex: 1;
        }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .status-success {
            background: #dcfce7;
            color: #166534;
        }
        
        .status-warning {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-info {
            background: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body class="rtl-layout">
    <div class="test-container">
        <header style="text-align: center; margin-bottom: 3rem;">
            <h1 style="color: #2563eb; font-family: 'Cairo', Arial, sans-serif; font-size: 2.5rem; margin-bottom: 1rem;">
                اختبار منشئ السيرة الذاتية العربي
            </h1>
            <p style="color: #6b7280; font-family: 'Cairo', Arial, sans-serif; font-size: 1.1rem;">
                اختبار شامل لجميع مميزات منشئ السيرة الذاتية مع الدعم الكامل للغة العربية
            </p>
        </header>

        <!-- Language Switching Test -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-language"></i>
                اختبار تبديل اللغة
            </h2>
            <p class="arabic-text">اختبر تبديل اللغة بين العربية والإنجليزية:</p>
            <div style="margin: 1rem 0;">
                <span class="status-indicator status-success">
                    <i class="fas fa-check"></i>
                    دعم RTL كامل
                </span>
                <span class="status-indicator status-success">
                    <i class="fas fa-check"></i>
                    خطوط عربية
                </span>
                <span class="status-indicator status-success">
                    <i class="fas fa-check"></i>
                    ترجمة ديناميكية
                </span>
            </div>
        </div>

        <!-- Form Validation Test -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-check-circle"></i>
                اختبار التحقق من النماذج
            </h2>
            <form class="demo-form" id="test-form">
                <div class="form-group">
                    <label for="test-name" class="arabic-text">الاسم الكامل *</label>
                    <input type="text" id="test-name" placeholder="أدخل اسمك الكامل" required class="arabic-text" data-field-name="الاسم الكامل">
                </div>
                <div class="form-group">
                    <label for="test-email" class="arabic-text">البريد الإلكتروني *</label>
                    <input type="email" id="test-email" placeholder="<EMAIL>" required class="arabic-text" data-field-name="البريد الإلكتروني">
                </div>
                <div class="form-group">
                    <label for="test-phone" class="arabic-text">رقم الهاتف</label>
                    <input type="tel" id="test-phone" placeholder="٠١٠١٤٨٤٠٢٦٩" class="arabic-text">
                </div>
                <div class="form-group">
                    <label for="test-summary" class="arabic-text">الملخص المهني</label>
                    <textarea id="test-summary" rows="4" placeholder="اكتب ملخصاً مهنياً مقنعاً..." class="arabic-text"></textarea>
                </div>
            </form>
            <button type="button" class="btn btn-primary" onclick="testFormValidation()">
                <i class="fas fa-check"></i>
                اختبار التحقق
            </button>
        </div>

        <!-- CV Preview Test -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-eye"></i>
                اختبار معاينة السيرة الذاتية
            </h2>
            <div class="feature-demo">
                <div>
                    <h3 style="color: #374151; margin-bottom: 1rem; font-family: 'Cairo', Arial, sans-serif;">
                        بيانات تجريبية
                    </h3>
                    <button type="button" class="btn btn-outline" onclick="loadSampleData()">
                        <i class="fas fa-download"></i>
                        تحميل بيانات تجريبية
                    </button>
                    <button type="button" class="btn btn-primary" onclick="generatePreview()" style="margin-right: 1rem;">
                        <i class="fas fa-eye"></i>
                        إنشاء معاينة
                    </button>
                </div>
                <div>
                    <h3 style="color: #374151; margin-bottom: 1rem; font-family: 'Cairo', Arial, sans-serif;">
                        اختبار PDF
                    </h3>
                    <button type="button" class="btn btn-success" onclick="testPDFExport()">
                        <i class="fas fa-file-pdf"></i>
                        تصدير PDF
                    </button>
                </div>
            </div>
            <div class="demo-preview" id="cv-preview-demo">
                <p style="text-align: center; color: #6b7280; font-family: 'Cairo', Arial, sans-serif;">
                    ستظهر معاينة السيرة الذاتية هنا...
                </p>
            </div>
        </div>

        <!-- Features Status -->
        <div class="test-section">
            <h2 class="test-title">
                <i class="fas fa-list-check"></i>
                حالة المميزات
            </h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div>
                    <h4 style="color: #374151; margin-bottom: 0.5rem; font-family: 'Cairo', Arial, sans-serif;">الأساسيات</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <span class="status-indicator status-success">
                            <i class="fas fa-check"></i>
                            دعم RTL
                        </span>
                        <span class="status-indicator status-success">
                            <i class="fas fa-check"></i>
                            خطوط عربية
                        </span>
                        <span class="status-indicator status-success">
                            <i class="fas fa-check"></i>
                            ترجمة واجهة
                        </span>
                    </div>
                </div>
                <div>
                    <h4 style="color: #374151; margin-bottom: 0.5rem; font-family: 'Cairo', Arial, sans-serif;">النماذج</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <span class="status-indicator status-success">
                            <i class="fas fa-check"></i>
                            إدخال عربي
                        </span>
                        <span class="status-indicator status-success">
                            <i class="fas fa-check"></i>
                            التحقق العربي
                        </span>
                        <span class="status-indicator status-success">
                            <i class="fas fa-check"></i>
                            رسائل خطأ عربية
                        </span>
                    </div>
                </div>
                <div>
                    <h4 style="color: #374151; margin-bottom: 0.5rem; font-family: 'Cairo', Arial, sans-serif;">التصدير</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <span class="status-indicator status-success">
                            <i class="fas fa-check"></i>
                            PDF عربي
                        </span>
                        <span class="status-indicator status-success">
                            <i class="fas fa-check"></i>
                            قوالب RTL
                        </span>
                        <span class="status-indicator status-info">
                            <i class="fas fa-info"></i>
                            طباعة عربية
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/translations.js"></script>
    <script src="js/arabic-validation.js"></script>
    <script src="js/arabic-templates.js"></script>
    <script src="js/arabic-pdf.js"></script>

    <script>
        // Sample CV data for testing
        const sampleCVData = {
            personal: {
                fullName: 'أحمد محمد الأشرافي',
                jobTitle: 'مطور برمجيات أول',
                email: '<EMAIL>',
                phone: '٠١٠١٤٨٤٠٢٦٩',
                location: 'القاهرة، مصر',
                website: 'https://ahmed.elashrafycv.com',
                linkedin: 'https://linkedin.com/in/ahmed-elashrafy',
                github: 'https://github.com/ahmed-elashrafy'
            },
            summary: 'مطور برمجيات متمرس مع أكثر من 5 سنوات من الخبرة في تطوير تطبيقات الويب والهاتف المحمول. خبرة واسعة في JavaScript وReact وNode.js. شغوف بإنشاء حلول تقنية مبتكرة وفعالة.',
            experience: [
                {
                    title: 'مطور برمجيات أول',
                    company: 'شركة التقنية المتقدمة',
                    location: 'القاهرة، مصر',
                    startDate: '2021',
                    endDate: '',
                    current: true,
                    description: 'قيادة فريق من 5 مطورين في تطوير تطبيقات ويب متقدمة باستخدام React وNode.js. تحسين أداء التطبيقات بنسبة 40% وتقليل وقت التحميل.'
                },
                {
                    title: 'مطور ويب',
                    company: 'استوديو الإبداع الرقمي',
                    location: 'الجيزة، مصر',
                    startDate: '2019',
                    endDate: '2021',
                    current: false,
                    description: 'تطوير مواقع ويب تفاعلية وتطبيقات ويب للعملاء. العمل مع فرق متعددة التخصصات لتسليم مشاريع عالية الجودة في الوقت المحدد.'
                }
            ],
            education: [
                {
                    degree: 'بكالوريوس علوم الحاسوب',
                    school: 'جامعة القاهرة',
                    location: 'القاهرة، مصر',
                    startDate: '2015',
                    endDate: '2019',
                    gpa: '3.8',
                    description: 'تخصص في هندسة البرمجيات وقواعد البيانات'
                }
            ],
            skills: [
                { name: 'JavaScript', level: 'expert' },
                { name: 'React', level: 'advanced' },
                { name: 'Node.js', level: 'advanced' },
                { name: 'Python', level: 'intermediate' },
                { name: 'MongoDB', level: 'advanced' },
                { name: 'Git', level: 'expert' }
            ],
            projects: [
                {
                    name: 'منصة التجارة الإلكترونية',
                    description: 'تطوير منصة تجارة إلكترونية شاملة باستخدام React وNode.js مع دعم المدفوعات الإلكترونية',
                    technologies: 'React, Node.js, MongoDB, Stripe',
                    url: 'https://ecommerce-demo.com',
                    startDate: '2022',
                    endDate: '2023'
                }
            ],
            languages: [
                { name: 'العربية', proficiency: 'native' },
                { name: 'الإنجليزية', proficiency: 'fluent' },
                { name: 'الفرنسية', proficiency: 'intermediate' }
            ]
        };

        function loadSampleData() {
            // Populate form with sample data
            document.getElementById('test-name').value = sampleCVData.personal.fullName;
            document.getElementById('test-email').value = sampleCVData.personal.email;
            document.getElementById('test-phone').value = sampleCVData.personal.phone;
            document.getElementById('test-summary').value = sampleCVData.summary;
            
            showMessage('تم تحميل البيانات التجريبية بنجاح', 'success');
        }

        function generatePreview() {
            if (!window.arabicTemplateManager) {
                showMessage('نظام القوالب غير متاح', 'error');
                return;
            }

            const previewContainer = document.getElementById('cv-preview-demo');
            const previewHTML = window.arabicTemplateManager.generatePreview('modern', sampleCVData);
            previewContainer.innerHTML = previewHTML;
            
            showMessage('تم إنشاء المعاينة بنجاح', 'success');
        }

        function testFormValidation() {
            const form = document.getElementById('test-form');
            const isValid = window.arabicFormValidator.validateForm(form, { preventDefault: () => {} });
            
            if (isValid) {
                showMessage('جميع الحقول صحيحة!', 'success');
            } else {
                showMessage('يرجى تصحيح الأخطاء في النموذج', 'error');
            }
        }

        function testPDFExport() {
            if (!window.arabicPDFExporter) {
                showMessage('نظام تصدير PDF غير متاح', 'error');
                return;
            }

            window.arabicPDFExporter.exportToPDF(sampleCVData, 'modern')
                .then(result => {
                    if (result.success) {
                        showMessage('تم تصدير PDF بنجاح', 'success');
                    } else {
                        showMessage('فشل في تصدير PDF: ' + result.message, 'error');
                    }
                })
                .catch(error => {
                    showMessage('خطأ في تصدير PDF', 'error');
                    console.error(error);
                });
        }

        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `status-indicator status-${type === 'success' ? 'success' : type === 'error' ? 'warning' : 'info'}`;
            messageDiv.style.position = 'fixed';
            messageDiv.style.top = '20px';
            messageDiv.style.left = '20px';
            messageDiv.style.zIndex = '1000';
            messageDiv.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'}"></i>
                ${message}
            `;
            
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Arabic CV Builder Test Page Loaded');
            console.log('Translation Manager:', window.translationManager ? 'Available' : 'Not Available');
            console.log('Arabic Form Validator:', window.arabicFormValidator ? 'Available' : 'Not Available');
            console.log('Arabic Template Manager:', window.arabicTemplateManager ? 'Available' : 'Not Available');
            console.log('Arabic PDF Exporter:', window.arabicPDFExporter ? 'Available' : 'Not Available');
        });
    </script>
</body>
</html>
