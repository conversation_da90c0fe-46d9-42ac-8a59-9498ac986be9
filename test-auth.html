<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Test - Elashrafy CV</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .btn {
            background: #2563eb;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .btn.secondary {
            background: #6b7280;
        }
        .btn.secondary:hover {
            background: #4b5563;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        .status.info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        .user-info {
            background: #f8fafc;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
        pre {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            font-size: 14px;
        }
    </style>
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
</head>
<body>
    <div class="container">
        <h1>🔐 Authentication System Test</h1>
        <p>This page tests the Elashrafy CV authentication system with the new Supabase backend.</p>

        <!-- Connection Status -->
        <div class="test-section">
            <h3>📡 Connection Status</h3>
            <div id="connection-status" class="status info">Checking connection...</div>
            <button class="btn secondary" onclick="checkConnection()">Refresh Connection</button>
        </div>

        <!-- Current User Status -->
        <div class="test-section">
            <h3>👤 Current User</h3>
            <div id="user-status" class="status info">Checking authentication status...</div>
            <div id="user-info" class="user-info" style="display: none;"></div>
            <button class="btn secondary" onclick="checkAuthStatus()">Refresh Status</button>
            <button class="btn secondary" onclick="signOutUser()" id="signout-btn" style="display: none;">Sign Out</button>
        </div>

        <!-- Sign Up Test -->
        <div class="test-section">
            <h3>📝 Sign Up Test</h3>
            <form id="signup-test-form">
                <div class="form-group">
                    <label for="test-signup-name">Full Name:</label>
                    <input type="text" id="test-signup-name" placeholder="Enter full name" value="Test User">
                </div>
                <div class="form-group">
                    <label for="test-signup-email">Email:</label>
                    <input type="email" id="test-signup-email" placeholder="Enter email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="test-signup-password">Password:</label>
                    <input type="password" id="test-signup-password" placeholder="Enter password" value="testpass123">
                </div>
                <button type="submit" class="btn">Test Sign Up</button>
            </form>
            <div id="signup-result"></div>
        </div>

        <!-- Sign In Test -->
        <div class="test-section">
            <h3>🔑 Sign In Test</h3>
            <form id="signin-test-form">
                <div class="form-group">
                    <label for="test-signin-email">Email:</label>
                    <input type="email" id="test-signin-email" placeholder="Enter email" value="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="test-signin-password">Password:</label>
                    <input type="password" id="test-signin-password" placeholder="Enter password" value="testpass123">
                </div>
                <button type="submit" class="btn">Test Sign In</button>
            </form>
            <div id="signin-result"></div>
        </div>

        <!-- Database Operations Test -->
        <div class="test-section">
            <h3>🗄️ Database Operations Test</h3>
            <button class="btn" onclick="testDatabaseOperations()">Test Database Access</button>
            <div id="database-result"></div>
        </div>

        <!-- Debug Information -->
        <div class="test-section">
            <h3>🐛 Debug Information</h3>
            <button class="btn secondary" onclick="showDebugInfo()">Show Debug Info</button>
            <div id="debug-info"></div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/supabase-config.js"></script>
    <script>
        // Wait for services to initialize
        let dbService = null;
        
        document.addEventListener('DOMContentLoaded', async () => {
            // Wait for database service
            await waitForService();
            
            // Initial checks
            await checkConnection();
            await checkAuthStatus();
            
            // Setup form handlers
            setupFormHandlers();
        });

        async function waitForService() {
            return new Promise((resolve) => {
                const check = () => {
                    if (window.dbService) {
                        dbService = window.dbService;
                        resolve();
                    } else {
                        setTimeout(check, 100);
                    }
                };
                check();
            });
        }

        async function checkConnection() {
            const statusEl = document.getElementById('connection-status');
            statusEl.textContent = 'Checking connection...';
            statusEl.className = 'status info';

            try {
                const health = await dbService.getServerHealth();
                if (health.status === 'healthy') {
                    statusEl.textContent = `✅ Connected to Supabase (${health.responseTime})`;
                    statusEl.className = 'status success';
                } else {
                    statusEl.textContent = `❌ Connection issues: ${health.error}`;
                    statusEl.className = 'status error';
                }
            } catch (error) {
                statusEl.textContent = `❌ Connection failed: ${error.message}`;
                statusEl.className = 'status error';
            }
        }

        async function checkAuthStatus() {
            const statusEl = document.getElementById('user-status');
            const infoEl = document.getElementById('user-info');
            const signoutBtn = document.getElementById('signout-btn');

            try {
                const { user, error } = await dbService.getCurrentUser();
                
                if (user) {
                    statusEl.textContent = '✅ User is authenticated';
                    statusEl.className = 'status success';
                    
                    infoEl.innerHTML = `
                        <strong>User ID:</strong> ${user.id}<br>
                        <strong>Email:</strong> ${user.email}<br>
                        <strong>Email Confirmed:</strong> ${user.email_confirmed_at ? 'Yes' : 'No'}<br>
                        <strong>Created:</strong> ${new Date(user.created_at).toLocaleString()}
                    `;
                    infoEl.style.display = 'block';
                    signoutBtn.style.display = 'inline-block';
                } else {
                    statusEl.textContent = '❌ No authenticated user';
                    statusEl.className = 'status error';
                    infoEl.style.display = 'none';
                    signoutBtn.style.display = 'none';
                }
            } catch (error) {
                statusEl.textContent = `❌ Auth check failed: ${error.message}`;
                statusEl.className = 'status error';
                infoEl.style.display = 'none';
                signoutBtn.style.display = 'none';
            }
        }

        function setupFormHandlers() {
            document.getElementById('signup-test-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await testSignUp();
            });

            document.getElementById('signin-test-form').addEventListener('submit', async (e) => {
                e.preventDefault();
                await testSignIn();
            });
        }

        async function testSignUp() {
            const resultEl = document.getElementById('signup-result');
            const name = document.getElementById('test-signup-name').value;
            const email = document.getElementById('test-signup-email').value;
            const password = document.getElementById('test-signup-password').value;

            resultEl.innerHTML = '<div class="status info">Testing sign up...</div>';

            try {
                const { data, error } = await dbService.signUp(email, password, name);
                
                if (error) {
                    resultEl.innerHTML = `<div class="status error">❌ Sign up failed: ${error.message}</div>`;
                } else {
                    resultEl.innerHTML = `<div class="status success">✅ Sign up successful! Check email for verification.</div>`;
                    setTimeout(checkAuthStatus, 1000);
                }
            } catch (error) {
                resultEl.innerHTML = `<div class="status error">❌ Sign up error: ${error.message}</div>`;
            }
        }

        async function testSignIn() {
            const resultEl = document.getElementById('signin-result');
            const email = document.getElementById('test-signin-email').value;
            const password = document.getElementById('test-signin-password').value;

            resultEl.innerHTML = '<div class="status info">Testing sign in...</div>';

            try {
                const { data, error } = await dbService.signIn(email, password);
                
                if (error) {
                    resultEl.innerHTML = `<div class="status error">❌ Sign in failed: ${error.message}</div>`;
                } else {
                    resultEl.innerHTML = `<div class="status success">✅ Sign in successful!</div>`;
                    setTimeout(checkAuthStatus, 1000);
                }
            } catch (error) {
                resultEl.innerHTML = `<div class="status error">❌ Sign in error: ${error.message}</div>`;
            }
        }

        async function signOutUser() {
            try {
                const { error } = await dbService.signOut();
                if (error) {
                    alert(`Sign out failed: ${error.message}`);
                } else {
                    alert('Signed out successfully!');
                    setTimeout(checkAuthStatus, 1000);
                }
            } catch (error) {
                alert(`Sign out error: ${error.message}`);
            }
        }

        async function testDatabaseOperations() {
            const resultEl = document.getElementById('database-result');
            resultEl.innerHTML = '<div class="status info">Testing database operations...</div>';

            try {
                // Test templates access
                const { data: templates, error } = await dbService.getTemplates();
                
                if (error) {
                    resultEl.innerHTML = `<div class="status error">❌ Database test failed: ${error.message}</div>`;
                } else {
                    resultEl.innerHTML = `
                        <div class="status success">✅ Database access successful!</div>
                        <p><strong>Templates found:</strong> ${templates ? templates.length : 0}</p>
                        <pre>${JSON.stringify(templates, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultEl.innerHTML = `<div class="status error">❌ Database error: ${error.message}</div>`;
            }
        }

        function showDebugInfo() {
            const debugEl = document.getElementById('debug-info');
            const info = {
                supabaseUrl: window.dbService?.supabase?.supabaseUrl,
                supabaseKey: window.dbService?.supabase?.supabaseKey ? '[HIDDEN]' : 'Not found',
                dbServiceExists: !!window.dbService,
                supabaseClientExists: !!window.dbService?.supabase,
                currentUrl: window.location.href,
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };

            debugEl.innerHTML = `<pre>${JSON.stringify(info, null, 2)}</pre>`;
        }
    </script>
</body>
</html>
