# 🚀 Database Setup Instructions

## Quick Setup Guide

Follow these steps to complete the database setup for the Elashrafy CV authentication system:

### Step 1: Access Supabase Dashboard

1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sign in to your account
3. Navigate to your project: `elashrafy-cv-builder` (ID: hoyzvlfeyzzqbmhmsypy)

### Step 2: Execute Database Setup SQL

1. In your Supabase dashboard, go to **SQL Editor**
2. Click **"New Query"**
3. Copy and paste the entire content from `database/complete-setup.sql`
4. Click **"Run"** to execute the SQL

### Step 3: Verify Setup

After running the SQL, verify the setup:

1. **Check Tables**: Go to **Table Editor** and confirm these tables exist:
   - `profiles`
   - `cvs`
   - `cv_templates`
   - `qr_codes`
   - `nfc_cards`
   - `cv_analytics`

2. **Check Templates**: In the `cv_templates` table, you should see 5 default templates

3. **Check Policies**: Go to **Authentication** > **Policies** and verify RLS policies are active

### Step 4: Test Authentication

1. Start the local server: `python -m http.server 8000`
2. Open `http://localhost:8000/test-auth.html`
3. Run through the test scenarios:
   - Connection test
   - Sign up test
   - Sign in test
   - Database operations test

### Step 5: Configure Email (Optional)

For production use, configure email settings:

1. Go to **Authentication** > **Settings**
2. Configure SMTP settings for email verification
3. Customize email templates

## Database Schema Overview

```sql
-- Core authentication and user data
profiles (
    id UUID PRIMARY KEY,           -- Links to auth.users
    full_name TEXT,
    avatar_url TEXT,
    phone TEXT,
    location TEXT,
    bio TEXT,
    website TEXT,
    linkedin TEXT,
    github TEXT,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- CV data storage
cvs (
    id UUID PRIMARY KEY,
    user_id UUID,                  -- Foreign key to auth.users
    title TEXT,
    cv_data JSONB,                 -- Flexible CV content storage
    template_id UUID,              -- Foreign key to cv_templates
    is_public BOOLEAN,
    views INTEGER,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Available CV templates
cv_templates (
    id UUID PRIMARY KEY,
    name TEXT,
    name_ar TEXT,                  -- Arabic name
    description TEXT,
    description_ar TEXT,           -- Arabic description
    category TEXT,
    template_data JSONB,           -- Template configuration
    preview_image TEXT,
    is_active BOOLEAN,
    is_premium BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- QR code management
qr_codes (
    id UUID PRIMARY KEY,
    user_id UUID,
    cv_id UUID,
    title TEXT,
    qr_url TEXT,
    qr_image TEXT,
    settings JSONB,
    created_at TIMESTAMP
)

-- NFC card management
nfc_cards (
    id UUID PRIMARY KEY,
    user_id UUID,
    cv_id UUID,
    card_name TEXT,
    description TEXT,
    nfc_url TEXT,
    is_active BOOLEAN,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
)

-- Analytics and tracking
cv_analytics (
    id UUID PRIMARY KEY,
    cv_id UUID,
    event_type TEXT,               -- 'view', 'download', 'share'
    event_data JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP
)
```

## Security Features

### Row Level Security (RLS) Policies

1. **Profiles**: Users can only access their own profile
2. **CVs**: Users can access their own CVs + public CVs
3. **QR Codes**: Users can only access their own QR codes
4. **NFC Cards**: Users can only access their own NFC cards
5. **Analytics**: Users can view analytics for their own CVs
6. **Templates**: Public read access for all active templates

### Automatic Triggers

1. **Profile Creation**: Automatically creates profile when user signs up
2. **Timestamp Updates**: Automatically updates `updated_at` fields
3. **View Tracking**: Tracks CV views and analytics

### Utility Functions

1. `increment_cv_views(cv_uuid)` - Safely increment CV view count
2. `get_user_stats(user_uuid)` - Get user statistics summary
3. `handle_new_user()` - Automatic profile creation trigger

## Troubleshooting

### Common Issues

1. **SQL Execution Errors**
   - Make sure you're in the correct project
   - Check for syntax errors in the SQL
   - Verify you have admin permissions

2. **RLS Policy Errors**
   - Ensure auth.users table exists
   - Check that RLS is enabled on tables
   - Verify policy syntax is correct

3. **Template Insertion Errors**
   - Check for character encoding issues
   - Verify JSONB format is valid
   - Ensure all required fields are provided

### Verification Queries

Run these queries to verify setup:

```sql
-- Check if tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Check if templates were inserted
SELECT name, category, is_active FROM cv_templates;

-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';

-- Check policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';
```

## Next Steps

After completing the database setup:

1. Test the authentication system thoroughly
2. Configure email settings for production
3. Set up monitoring and logging
4. Deploy to production environment
5. Set up backup procedures

## Support

If you encounter any issues during setup:

1. Check the Supabase logs in the dashboard
2. Review the `AUTHENTICATION_GUIDE.md` for detailed troubleshooting
3. Use the test page to diagnose specific issues
4. Contact support if needed

---

**Important**: Keep your Supabase credentials secure and never commit them to public repositories.
