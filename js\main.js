// Main Application JavaScript
class ElashrafyCV {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupSmoothScrolling();
        this.setupMobileNavigation();
        this.setupAnimations();
        this.loadTemplates();
    }

    setupEventListeners() {
        // Navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                if (link.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    const targetId = link.getAttribute('href').substring(1);
                    this.scrollToSection(targetId);
                }
            });
        });

        // Watch demo button
        const watchDemoBtn = document.getElementById('watch-demo-btn');
        if (watchDemoBtn) {
            watchDemoBtn.addEventListener('click', () => {
                this.showDemoModal();
            });
        }

        // Template cards
        const templateCards = document.querySelectorAll('.template-card .btn');
        templateCards.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleTemplateSelection(btn);
            });
        });

        // Feature links
        const featureLinks = document.querySelectorAll('.feature-link');
        featureLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href && href !== '#') {
                    // Check if user is authenticated for protected pages
                    if (href.includes('pages/') && window.authManager && !window.authManager.isAuthenticated()) {
                        e.preventDefault();
                        window.authManager.showErrorMessage('Please login to access this feature');
                        window.authManager.showLoginForm();
                        window.authManager.openAuthModal();
                    }
                }
            });
        });

        // Pricing buttons
        const pricingButtons = document.querySelectorAll('.pricing-card .btn');
        pricingButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handlePricingSelection(btn);
            });
        });

        // Contact form (if exists)
        const contactForm = document.getElementById('contact-form');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleContactForm(e);
            });
        }

        // Newsletter signup (if exists)
        const newsletterForm = document.getElementById('newsletter-form');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleNewsletterSignup(e);
            });
        }

        // Scroll to top button
        this.setupScrollToTop();

        // Window events
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });

        window.addEventListener('resize', () => {
            this.handleResize();
        });
    }

    setupSmoothScrolling() {
        // Enable smooth scrolling for anchor links
        document.documentElement.style.scrollBehavior = 'smooth';
    }

    setupMobileNavigation() {
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');

        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });

            // Close mobile menu when clicking on a link
            const navLinks = navMenu.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    navToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                });
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                    navToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                }
            });
        }
    }

    setupAnimations() {
        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        const animateElements = document.querySelectorAll('.feature-card, .template-card, .pricing-card');
        animateElements.forEach(el => {
            observer.observe(el);
        });

        // Add CSS for animations
        if (!document.getElementById('animation-styles')) {
            const styles = document.createElement('style');
            styles.id = 'animation-styles';
            styles.textContent = `
                .feature-card,
                .template-card,
                .pricing-card {
                    opacity: 0;
                    transform: translateY(30px);
                    transition: all 0.6s ease-out;
                }
                .feature-card.animate-in,
                .template-card.animate-in,
                .pricing-card.animate-in {
                    opacity: 1;
                    transform: translateY(0);
                }
                .feature-card:nth-child(1) { transition-delay: 0.1s; }
                .feature-card:nth-child(2) { transition-delay: 0.2s; }
                .feature-card:nth-child(3) { transition-delay: 0.3s; }
                .feature-card:nth-child(4) { transition-delay: 0.4s; }
                .feature-card:nth-child(5) { transition-delay: 0.5s; }
                .feature-card:nth-child(6) { transition-delay: 0.6s; }
            `;
            document.head.appendChild(styles);
        }
    }

    scrollToSection(sectionId) {
        const section = document.getElementById(sectionId);
        if (section) {
            const headerHeight = document.querySelector('.header').offsetHeight;
            const targetPosition = section.offsetTop - headerHeight - 20;
            
            window.scrollTo({
                top: targetPosition,
                behavior: 'smooth'
            });
        }
    }

    handleScroll() {
        const header = document.querySelector('.header');
        const scrollTop = window.pageYOffset;

        // Add/remove header shadow on scroll
        if (scrollTop > 10) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }

        // Update active navigation link
        this.updateActiveNavLink();

        // Show/hide scroll to top button
        this.updateScrollToTopButton();
    }

    updateActiveNavLink() {
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.nav-link');
        
        let currentSection = '';
        const scrollPosition = window.pageYOffset + 100;

        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            
            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                currentSection = section.getAttribute('id');
            }
        });

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${currentSection}`) {
                link.classList.add('active');
            }
        });
    }

    setupScrollToTop() {
        // Create scroll to top button
        const scrollToTopBtn = document.createElement('button');
        scrollToTopBtn.id = 'scroll-to-top';
        scrollToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
        scrollToTopBtn.className = 'scroll-to-top-btn';
        scrollToTopBtn.style.display = 'none';
        
        document.body.appendChild(scrollToTopBtn);

        scrollToTopBtn.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Add styles
        if (!document.getElementById('scroll-to-top-styles')) {
            const styles = document.createElement('style');
            styles.id = 'scroll-to-top-styles';
            styles.textContent = `
                .scroll-to-top-btn {
                    position: fixed;
                    bottom: 30px;
                    right: 30px;
                    width: 50px;
                    height: 50px;
                    background: var(--primary-color);
                    color: white;
                    border: none;
                    border-radius: 50%;
                    cursor: pointer;
                    font-size: 18px;
                    box-shadow: var(--shadow-lg);
                    transition: all 0.3s ease;
                    z-index: 1000;
                    opacity: 0;
                    transform: translateY(20px);
                }
                .scroll-to-top-btn.visible {
                    opacity: 1;
                    transform: translateY(0);
                }
                .scroll-to-top-btn:hover {
                    background: var(--primary-dark);
                    transform: translateY(-2px);
                }
            `;
            document.head.appendChild(styles);
        }
    }

    updateScrollToTopButton() {
        const scrollToTopBtn = document.getElementById('scroll-to-top');
        if (scrollToTopBtn) {
            if (window.pageYOffset > 300) {
                scrollToTopBtn.style.display = 'block';
                setTimeout(() => scrollToTopBtn.classList.add('visible'), 10);
            } else {
                scrollToTopBtn.classList.remove('visible');
                setTimeout(() => {
                    if (!scrollToTopBtn.classList.contains('visible')) {
                        scrollToTopBtn.style.display = 'none';
                    }
                }, 300);
            }
        }
    }

    handleResize() {
        // Handle responsive adjustments
        const navMenu = document.getElementById('nav-menu');
        const navToggle = document.getElementById('nav-toggle');

        if (window.innerWidth > 768) {
            if (navMenu) navMenu.classList.remove('active');
            if (navToggle) navToggle.classList.remove('active');
        }
    }

    async loadTemplates() {
        // Load and display templates from database
        try {
            if (window.dbService) {
                const { data: templates, error } = await window.dbService.getTemplates();
                if (error) throw error;

                if (templates && templates.length > 0) {
                    this.displayTemplates(templates.slice(0, 3)); // Show first 3 on homepage
                }
            }
        } catch (error) {
            console.error('Error loading templates:', error);
        }
    }

    displayTemplates(templates) {
        const templatesGrid = document.querySelector('.templates-grid');
        if (!templatesGrid || !templates) return;

        // Clear existing templates
        templatesGrid.innerHTML = '';

        templates.forEach(template => {
            const templateCard = this.createTemplateCard(template);
            templatesGrid.appendChild(templateCard);
        });
    }

    createTemplateCard(template) {
        const card = document.createElement('div');
        card.className = 'template-card';
        card.innerHTML = `
            <div class="template-preview">
                <div class="template-mockup ${template.name.toLowerCase().includes('modern') ? 'modern' : template.name.toLowerCase().includes('creative') ? 'creative' : 'classic'}">
                    <div class="mockup-header"></div>
                    <div class="mockup-content">
                        <div class="mockup-line"></div>
                        <div class="mockup-line short"></div>
                        <div class="mockup-line"></div>
                    </div>
                </div>
            </div>
            <h3 class="template-name">${template.name}</h3>
            <p class="template-description">${template.description}</p>
            <button class="btn btn-outline" data-template-id="${template.id}">Use Template</button>
        `;

        // Add event listener to the button
        const btn = card.querySelector('.btn');
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleTemplateSelection(btn);
        });

        return card;
    }

    handleTemplateSelection(button) {
        const templateId = button.dataset.templateId;
        
        if (window.authManager && !window.authManager.isAuthenticated()) {
            window.authManager.showErrorMessage('Please login to use templates');
            window.authManager.showLoginForm();
            window.authManager.openAuthModal();
            return;
        }

        // Redirect to CV builder with template
        const url = templateId ? 
            `pages/cv-builder.html?template=${templateId}` : 
            'pages/cv-builder.html';
        window.location.href = url;
    }

    handlePricingSelection(button) {
        const card = button.closest('.pricing-card');
        const planName = card.querySelector('.pricing-title').textContent;

        if (planName === 'Free') {
            if (window.authManager && !window.authManager.isAuthenticated()) {
                window.authManager.showSignupForm();
                window.authManager.openAuthModal();
            } else {
                window.location.href = 'pages/dashboard.html';
            }
        } else if (planName === 'Pro') {
            // Handle Pro plan upgrade
            this.showMessage('Pro plan upgrade coming soon!', 'info');
        } else if (planName === 'Enterprise') {
            // Handle Enterprise contact
            this.showMessage('Please contact us for Enterprise pricing', 'info');
        }
    }

    showDemoModal() {
        // Create and show demo modal
        const modal = document.createElement('div');
        modal.className = 'modal';
        modal.id = 'demo-modal';
        modal.innerHTML = `
            <div class="modal-content" style="max-width: 800px;">
                <span class="close">&times;</span>
                <h2>Product Demo</h2>
                <div class="demo-video">
                    <div style="padding: 60px; text-align: center; background: var(--gray-100); border-radius: var(--radius-lg);">
                        <i class="fas fa-play-circle" style="font-size: 4rem; color: var(--primary-color); margin-bottom: 1rem;"></i>
                        <p>Demo video coming soon!</p>
                        <p style="color: var(--gray-500); margin-top: 1rem;">Experience the power of Elashrafy CV with our interactive demo.</p>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'block';

        // Close modal events
        const closeBtn = modal.querySelector('.close');
        closeBtn.addEventListener('click', () => {
            modal.remove();
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    showMessage(message, type = 'info') {
        if (window.authManager) {
            window.authManager.showMessage(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    // Utility methods
    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.elashrafyCV = new ElashrafyCV();
});

// Add header scroll effect styles
if (!document.getElementById('header-scroll-styles')) {
    const styles = document.createElement('style');
    styles.id = 'header-scroll-styles';
    styles.textContent = `
        .header.scrolled {
            box-shadow: var(--shadow-md);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        .nav-link.active {
            color: var(--primary-color);
        }
        .nav-link.active::after {
            width: 100%;
        }
    `;
    document.head.appendChild(styles);
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ElashrafyCV;
}
