# ⚡ تنفيذ فوري - إعداد قاعدة البيانات

## 🎯 خطوة واحدة فقط لتشغيل النظام

### 1. افتح Supabase Dashboard
اذهب إلى: https://supabase.com/dashboard/project/hoyzvlfeyzzqbmhmsypy

### 2. اذهب إلى SQL Editor
اضغط على **SQL Editor** في القائمة الجانبية

### 3. انسخ والصق هذا الكود
```sql
-- إعداد قاعدة البيانات الإنتاجية لتطبيق Elashrafy CV
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- إنشاء جدول الملفات الشخصية
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT NOT NULL,
    avatar_url TEXT,
    phone TEXT,
    location TEXT,
    bio TEXT,
    website TEXT,
    linkedin TEXT,
    github TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول قوالب السيرة الذاتية
CREATE TABLE IF NOT EXISTS public.cv_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    category TEXT NOT NULL DEFAULT 'modern',
    template_data JSONB NOT NULL DEFAULT '{}',
    preview_image TEXT,
    is_active BOOLEAN DEFAULT true,
    is_premium BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- تفعيل Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للملفات الشخصية
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;

CREATE POLICY "Users can view own profile" ON public.profiles 
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles 
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

-- القوالب متاحة للقراءة للجميع
DROP POLICY IF EXISTS "Templates are public" ON public.cv_templates;
CREATE POLICY "Templates are public" ON public.cv_templates 
    FOR SELECT USING (is_active = true);

-- دالة لإنشاء ملف شخصي تلقائياً عند التسجيل
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- مشغل لإنشاء الملف الشخصي تلقائياً
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- إدراج القوالب الافتراضية
INSERT INTO public.cv_templates (name, name_ar, description, description_ar, category, template_data, is_active) VALUES
(
    'Modern Professional',
    'مهني حديث',
    'Clean and modern design perfect for tech professionals',
    'تصميم نظيف وحديث مثالي للمهنيين التقنيين',
    'modern',
    '{"colors": {"primary": "#2563eb", "secondary": "#64748b"}, "fonts": {"primary": "Cairo", "secondary": "Inter"}}',
    true
),
(
    'Classic Executive',
    'تنفيذي كلاسيكي',
    'Traditional layout ideal for executive positions',
    'تخطيط تقليدي مثالي للمناصب التنفيذية',
    'classic',
    '{"colors": {"primary": "#f59e0b", "secondary": "#6b7280"}, "fonts": {"primary": "Noto Sans Arabic", "secondary": "Georgia"}}',
    true
),
(
    'Creative Designer',
    'مصمم إبداعي',
    'Bold and creative design for creative professionals',
    'تصميم جريء وإبداعي للمهنيين المبدعين',
    'creative',
    '{"colors": {"primary": "#10b981", "secondary": "#8b5cf6"}, "fonts": {"primary": "Cairo", "secondary": "Poppins"}}',
    true
)
ON CONFLICT DO NOTHING;

-- منح الصلاحيات المطلوبة
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
```

### 4. اضغط "Run"
اضغط على زر **"Run"** لتنفيذ الكود

### 5. تعطيل تأكيد البريد الإلكتروني (اختياري)
- اذهب إلى **Authentication** > **Settings**
- ألغ تفعيل **"Enable email confirmations"**
- اضغط **"Save"**

## ✅ انتهيت! 

الآن التطبيق جاهز للاستخدام على: http://localhost:8000

### اختبار سريع:
1. اضغط **"إنشاء حساب"**
2. املأ البيانات:
   - الاسم: `مستخدم تجريبي`
   - البريد: `<EMAIL>`
   - كلمة المرور: `123456`
3. اضغط **"إنشاء حساب"**
4. سجل الدخول بنفس البيانات
5. ستتم إعادة توجيهك تلقائياً للوحة التحكم

🎉 **النظام يعمل بشكل مثالي الآن!**
