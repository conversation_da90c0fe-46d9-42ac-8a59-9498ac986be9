<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نشر فوري - Elashrafy CV</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .deploy-option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }
        .deploy-option:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.2);
        }
        .deploy-btn {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
            transition: background 0.3s ease;
        }
        .deploy-btn:hover {
            background: #0056b3;
        }
        .deploy-btn.netlify {
            background: #00c7b7;
        }
        .deploy-btn.netlify:hover {
            background: #00a085;
        }
        .deploy-btn.vercel {
            background: #000;
        }
        .deploy-btn.vercel:hover {
            background: #333;
        }
        .deploy-btn.github {
            background: #24292e;
        }
        .deploy-btn.github:hover {
            background: #1a1e22;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .step {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .step h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 نشر فوري لتطبيق Elashrafy CV</h1>
            <p>اختر إحدى الطرق التالية لنشر التطبيق على الإنترنت فوراً</p>
        </div>

        <div class="status info">
            📋 التطبيق جاهز للنشر مع قاعدة البيانات المكتملة
        </div>

        <!-- خيار 1: Netlify Drop -->
        <div class="deploy-option">
            <h3>🎯 الطريقة الأسرع - Netlify Drop (موصى بها)</h3>
            <p>نشر فوري خلال دقيقة واحدة بدون تسجيل</p>
            
            <div class="step">
                <h4>الخطوة 1:</h4>
                <p>اضغط على الرابط التالي:</p>
                <a href="https://app.netlify.com/drop" target="_blank" class="deploy-btn netlify">
                    🚀 افتح Netlify Drop
                </a>
            </div>

            <div class="step">
                <h4>الخطوة 2:</h4>
                <p>اسحب وأفلت مجلد المشروع بالكامل في الصفحة</p>
                <p><strong>مهم:</strong> تأكد من سحب المجلد الذي يحتوي على ملف index.html</p>
            </div>

            <div class="step">
                <h4>الخطوة 3:</h4>
                <p>انتظر انتهاء الرفع (30-60 ثانية)</p>
                <p>ستحصل على رابط مباشر للموقع</p>
            </div>
        </div>

        <!-- خيار 2: Vercel -->
        <div class="deploy-option">
            <h3>⚡ Vercel - نشر سريع</h3>
            <p>نشر احترافي مع أداء عالي</p>
            
            <div class="step">
                <h4>الخطوات:</h4>
                <ol>
                    <li>اضغط على الزر أدناه</li>
                    <li>سجل دخول بحساب GitHub (إذا لم يكن لديك حساب)</li>
                    <li>اختر "Deploy" وانتظر انتهاء النشر</li>
                </ol>
                <a href="https://vercel.com/new" target="_blank" class="deploy-btn vercel">
                    ⚡ نشر على Vercel
                </a>
            </div>
        </div>

        <!-- خيار 3: GitHub Pages -->
        <div class="deploy-option">
            <h3>📚 GitHub Pages - نشر مجاني</h3>
            <p>نشر مجاني مدى الحياة مع GitHub</p>
            
            <div class="step">
                <h4>الخطوات:</h4>
                <ol>
                    <li>أنشئ حساب على GitHub إذا لم يكن لديك</li>
                    <li>أنشئ مستودع جديد (repository)</li>
                    <li>ارفع جميع ملفات المشروع</li>
                    <li>فعل GitHub Pages من الإعدادات</li>
                </ol>
                <a href="https://github.com/new" target="_blank" class="deploy-btn github">
                    📚 إنشاء مستودع GitHub
                </a>
            </div>
        </div>

        <!-- خيار 4: Firebase Hosting -->
        <div class="deploy-option">
            <h3>🔥 Firebase Hosting</h3>
            <p>نشر مع Google Firebase</p>
            
            <div class="step">
                <h4>الخطوات:</h4>
                <ol>
                    <li>اذهب إلى Firebase Console</li>
                    <li>أنشئ مشروع جديد</li>
                    <li>فعل Hosting</li>
                    <li>ارفع الملفات</li>
                </ol>
                <a href="https://console.firebase.google.com" target="_blank" class="deploy-btn">
                    🔥 Firebase Console
                </a>
            </div>
        </div>

        <!-- معلومات قاعدة البيانات -->
        <div class="status success">
            <h3>✅ قاعدة البيانات جاهزة</h3>
            <p><strong>Supabase URL:</strong> https://hoyzvlfeyzzqbmhmsypy.supabase.co</p>
            <p><strong>الحالة:</strong> مفعلة ومكتملة مع جميع الجداول والسياسات</p>
        </div>

        <!-- اختبار سريع -->
        <div class="deploy-option">
            <h3>🧪 اختبار سريع للتطبيق</h3>
            <p>تأكد من أن التطبيق يعمل محلياً قبل النشر</p>
            
            <div class="step">
                <h4>تشغيل محلي:</h4>
                <pre>python -m http.server 8000</pre>
                <p>ثم افتح: <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></p>
            </div>

            <div class="step">
                <h4>اختبار المميزات:</h4>
                <ul>
                    <li>✅ تسجيل حساب جديد</li>
                    <li>✅ تسجيل الدخول</li>
                    <li>✅ الوصول للوحة التحكم</li>
                    <li>✅ منشئ السيرة الذاتية</li>
                </ul>
            </div>
        </div>

        <!-- نصائح مهمة -->
        <div class="status info">
            <h3>💡 نصائح مهمة للنشر</h3>
            <ul>
                <li><strong>تأكد من رفع جميع الملفات:</strong> index.html, js/, css/, pages/</li>
                <li><strong>لا تغير إعدادات Supabase:</strong> قاعدة البيانات مكتملة ومفعلة</li>
                <li><strong>اختبر التطبيق:</strong> تأكد من عمل التسجيل وتسجيل الدخول</li>
                <li><strong>احفظ الرابط:</strong> احفظ رابط الموقع بعد النشر</li>
            </ul>
        </div>

        <!-- معلومات الدعم -->
        <div class="status">
            <h3>📞 الدعم الفني</h3>
            <p><strong>المطور:</strong> محمد الأشرفي</p>
            <p><strong>الهاتف:</strong> 01014840269</p>
            <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
        </div>
    </div>

    <script>
        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.deploy-option').forEach(option => {
            option.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
            });
            
            option.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });

        // فحص حالة الاتصال
        window.addEventListener('load', function() {
            if (navigator.onLine) {
                console.log('✅ متصل بالإنترنت - جاهز للنشر');
            } else {
                alert('⚠️ تحقق من اتصال الإنترنت قبل النشر');
            }
        });
    </script>
</body>
</html>
