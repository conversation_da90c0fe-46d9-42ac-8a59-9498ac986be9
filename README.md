# Elashrafy CV - منشئ السيرة الذاتية

منصة شاملة لإنشاء وإدارة السيرة الذاتية بتقنيات حديثة مع دعم كامل للغة العربية وتقنيات RTL.

## المميزات الرئيسية

### 🎨 **منشئ السيرة الذاتية المتقدم**
- واجهة سهلة الاستخدام مع دعم كامل للغة العربية
- قوالب متنوعة ومصممة بعناية
- معاينة فورية للتغييرات
- تصدير PDF بجودة عالية
- دعم الصور الشخصية

### 🌐 **دعم اللغة العربية المتقدم**
- تخطيط RTL (من اليمين إلى اليسار)
- خطوط عربية جميلة (Cairo, Noto Sans Arabic)
- تحقق من صحة النصوص العربية
- تنسيق تلقائي للنصوص المختلطة
- دعم الأرقام العربية والإنجليزية

### 📱 **تصميم متجاوب**
- يعمل على جميع الأجهزة (هاتف، تابلت، كمبيوتر)
- تصميم Mobile-First
- واجهة مستخدم حديثة ومتجاوبة
- تجربة مستخدم محسنة

### 🔐 **نظام مصادقة متقدم - محدث ومحسن**
- ✅ **تم إصلاح جميع مشاكل تسجيل الدخول**
- 🛡️ **أمان متقدم**: Row Level Security (RLS) مع Supabase
- 🔑 **إدارة JWT**: معالجة آمنة للرموز المميزة
- 📧 **تأكيد البريد الإلكتروني**: تفعيل الحسابات عبر البريد
- 🚨 **معالجة الأخطاء**: رسائل خطأ واضحة ومفيدة
- 🔄 **إدارة الجلسات**: استمرارية تسجيل الدخول
- 🏥 **مراقبة الصحة**: فحص حالة الاتصال بقاعدة البيانات

### 📊 **لوحة تحكم شاملة**
- إحصائيات مفصلة
- إدارة السير الذاتية
- تتبع المشاهدات
- إجراءات سريعة

### 🔗 **تقنيات المشاركة الذكية**
- **مولد رموز QR**: إنشاء رموز QR قابلة للتخصيص
- **إدارة بطاقات NFC**: ربط السيرة الذاتية ببطاقات NFC
- مشاركة سهلة عبر الروابط
- تتبع المشاهدات والتفاعل

## التقنيات المستخدمة

### Frontend
- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتخطيط
- **JavaScript (ES6+)** - التفاعل والوظائف
- **Font Awesome** - الأيقونات
- **Google Fonts** - الخطوط العربية والإنجليزية

### Backend & Database
- **Supabase** - قاعدة البيانات والمصادقة
- **PostgreSQL** - قاعدة البيانات الرئيسية
- **Supabase Storage** - تخزين الملفات والصور

### Libraries & Tools
- **QRCode.js** - إنشاء رموز QR
- **html2canvas** - تحويل HTML إلى صورة
- **jsPDF** - إنشاء ملفات PDF
- **Web NFC API** - تقنية NFC

## هيكل المشروع

```
CV.h/
├── index.html              # الصفحة الرئيسية
├── pages/                  # صفحات التطبيق
│   ├── cv-builder.html     # منشئ السيرة الذاتية
│   ├── dashboard.html      # لوحة التحكم
│   ├── templates.html      # قوالب السيرة الذاتية
│   ├── qr-generator.html   # مولد رموز QR
│   ├── nfc-manager.html    # إدارة بطاقات NFC
│   └── cv-view.html        # عرض السيرة الذاتية
├── css/                    # ملفات التصميم
│   ├── style.css           # التصميم الأساسي
│   ├── arabic.css          # دعم اللغة العربية
│   ├── cv-builder.css      # تصميم منشئ السيرة
│   ├── dashboard.css       # تصميم لوحة التحكم
│   └── responsive.css      # التصميم المتجاوب
├── js/                     # ملفات JavaScript
│   ├── main.js             # الوظائف الأساسية
│   ├── auth.js             # نظام المصادقة
│   ├── cv-builder.js       # منشئ السيرة الذاتية
│   ├── dashboard.js        # لوحة التحكم
│   ├── db-service.js       # خدمات قاعدة البيانات
│   ├── translations.js     # الترجمات
│   ├── arabic-validation.js # التحقق من النصوص العربية
│   ├── arabic-templates.js # قوالب السيرة العربية
│   └── arabic-pdf.js       # تصدير PDF بالعربية
└── assets/                 # الملفات الثابتة
    └── templates/          # صور قوالب السيرة
```

## قاعدة البيانات

### الجداول الرئيسية

#### `profiles` - ملفات المستخدمين
```sql
- id (UUID, Primary Key)
- full_name (TEXT)
- avatar_url (TEXT)
- phone (TEXT)
- location (TEXT)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### `cvs` - السير الذاتية
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key)
- title (TEXT)
- cv_data (JSONB)
- template_id (UUID)
- is_public (BOOLEAN)
- views (INTEGER)
- created_at (TIMESTAMP)
- updated_at (TIMESTAMP)
```

#### `cv_templates` - قوالب السيرة الذاتية
```sql
- id (UUID, Primary Key)
- name (TEXT)
- description (TEXT)
- template_data (JSONB)
- is_active (BOOLEAN)
- created_at (TIMESTAMP)
```

#### `qr_codes` - رموز QR
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key)
- cv_id (UUID, Foreign Key)
- title (TEXT)
- qr_url (TEXT)
- qr_image (TEXT)
- settings (JSONB)
- created_at (TIMESTAMP)
```

#### `nfc_cards` - بطاقات NFC
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key)
- cv_id (UUID, Foreign Key)
- card_name (TEXT)
- description (TEXT)
- nfc_url (TEXT)
- is_active (BOOLEAN)
- created_at (TIMESTAMP)
```

## 🔧 التحديثات الأخيرة - إصلاح نظام المصادقة

### ✅ المشاكل التي تم إصلاحها

1. **مشكلة خدمات قاعدة البيانات المتضاربة**
   - تم دمج `DatabaseService` المتضاربة في ملف واحد
   - تحسين معالجة الأخطاء والتحقق من صحة البيانات

2. **إعدادات Supabase غير صحيحة**
   - تم إنشاء مشروع Supabase جديد
   - تحديث جميع المفاتيح والروابط

3. **قاعدة البيانات غير مكتملة**
   - إنشاء جميع الجداول المطلوبة
   - تطبيق سياسات Row Level Security
   - إضافة قوالب السيرة الذاتية الافتراضية

4. **معالجة الأخطاء الضعيفة**
   - رسائل خطأ واضحة ومفيدة
   - تحديد الأخطاء على مستوى الحقول
   - مؤشرات بصرية للأخطاء

5. **مشاكل الأمان**
   - تطبيق Row Level Security على جميع الجداول
   - إدارة آمنة لرموز JWT
   - التحقق من صحة المدخلات

### 🚀 ميزات جديدة

- **صفحة اختبار المصادقة**: `test-auth.html` لاختبار النظام
- **مراقبة صحة الاتصال**: فحص حالة قاعدة البيانات
- **إنشاء الملف الشخصي التلقائي**: عند تسجيل حساب جديد
- **إحصائيات المستخدم**: دالة لجلب إحصائيات شاملة

### 📚 ملفات التوثيق الجديدة

- `AUTHENTICATION_GUIDE.md` - دليل شامل لنظام المصادقة
- `setup-database.md` - تعليمات إعداد قاعدة البيانات
- `database/complete-setup.sql` - سكريبت إعداد قاعدة البيانات الكامل

## التثبيت والإعداد

### 1. إعداد Supabase

1. إنشاء مشروع جديد في [Supabase](https://supabase.com)
2. تشغيل SQL scripts لإنشاء الجداول
3. إعداد Row Level Security (RLS)
4. إعداد Storage buckets للصور

### 2. إعداد المشروع

1. استنساخ المشروع:
```bash
git clone [repository-url]
cd CV.h
```

2. تحديث إعدادات Supabase في `js/supabase-config.js`:
```javascript
const SUPABASE_URL = 'your-supabase-url'
const SUPABASE_ANON_KEY = 'your-supabase-anon-key'
```

3. تشغيل المشروع على خادم محلي:
```bash
# باستخدام Python
python -m http.server 8000

# أو باستخدام Node.js
npx serve .

# أو باستخدام Live Server في VS Code
```

### 3. إعداد قاعدة البيانات

تشغيل SQL التالي في Supabase SQL Editor:

```sql
-- إنشاء جدول الملفات الشخصية
CREATE TABLE profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    avatar_url TEXT,
    phone TEXT,
    location TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول السير الذاتية
CREATE TABLE cvs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    cv_data JSONB NOT NULL DEFAULT '{}',
    template_id UUID,
    is_public BOOLEAN DEFAULT false,
    views INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول قوالب السيرة الذاتية
CREATE TABLE cv_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    template_data JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول رموز QR
CREATE TABLE qr_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    qr_url TEXT NOT NULL,
    qr_image TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول بطاقات NFC
CREATE TABLE nfc_cards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    card_name TEXT NOT NULL,
    description TEXT,
    nfc_url TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إعداد Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE cvs ENABLE ROW LEVEL SECURITY;
ALTER TABLE qr_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE nfc_cards ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can view own CVs" ON cvs FOR SELECT USING (auth.uid() = user_id OR is_public = true);
CREATE POLICY "Users can insert own CVs" ON cvs FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own CVs" ON cvs FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own CVs" ON cvs FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own QR codes" ON qr_codes FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own QR codes" ON qr_codes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can delete own QR codes" ON qr_codes FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own NFC cards" ON nfc_cards FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own NFC cards" ON nfc_cards FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own NFC cards" ON nfc_cards FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own NFC cards" ON nfc_cards FOR DELETE USING (auth.uid() = user_id);
```

## الاستخدام

### 1. إنشاء حساب جديد
- انتقل إلى الصفحة الرئيسية
- انقر على "إنشاء حساب"
- أدخل البيانات المطلوبة

### 2. إنشاء سيرة ذاتية
- انتقل إلى "منشئ السيرة الذاتية"
- اختر قالب مناسب
- أدخل بياناتك الشخصية
- أضف خبراتك ومهاراتك
- احفظ وصدّر السيرة الذاتية

### 3. إنشاء رمز QR
- انتقل إلى "مولد QR"
- اختر السيرة الذاتية
- خصص إعدادات الرمز
- احفظ وحمّل الرمز

### 4. إدارة بطاقات NFC
- انتقل إلى "إدارة NFC"
- اختر السيرة الذاتية
- أنشئ بطاقة NFC جديدة
- اربط البطاقة بسيرتك الذاتية

## المساهمة

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني
- مراجعة الوثائق

## الشكر والتقدير

- **Supabase** - Backend as a Service
- **Font Awesome** - الأيقونات
- **Google Fonts** - الخطوط العربية
- **QRCode.js** - مكتبة رموز QR
- **jsPDF** - مكتبة PDF

---

تم التطوير بواسطة **Elashrafy** مع ❤️ للمجتمع العربي
