/* Arabic RTL Support and Typography */

/* Arabic Font Variables */
:root {
    --arabic-font-primary: 'Cairo', 'Noto Sans Arabic', Arial, sans-serif;
    --arabic-font-secondary: 'Noto Sans Arabic', 'Cairo', Arial, sans-serif;
    --arabic-font-decorative: '<PERSON><PERSON>', 'Noto Sans Arabic', serif;
}

/* RTL Layout Base */
.rtl-layout {
    direction: rtl;
    text-align: right;
    font-family: var(--arabic-font-primary);
}

/* Arabic Typography */
.arabic-text {
    font-family: var(--arabic-font-primary);
    direction: rtl;
    text-align: right;
    line-height: 1.8;
    letter-spacing: 0.02em;
}

.arabic-heading {
    font-family: var(--arabic-font-primary);
    font-weight: 600;
    direction: rtl;
    text-align: right;
}

.arabic-decorative {
    font-family: var(--arabic-font-decorative);
    direction: rtl;
    text-align: right;
}

/* RTL Navigation */
.rtl-layout .nav-container {
    direction: rtl;
}

.rtl-layout .nav-list {
    flex-direction: row-reverse;
}

.rtl-layout .nav-auth {
    flex-direction: row-reverse;
}

.rtl-layout .nav-toggle {
    order: -1;
}

/* RTL Buttons and Links */
.rtl-layout .btn {
    direction: rtl;
}

.rtl-layout .hero-buttons {
    flex-direction: row-reverse;
}

.rtl-layout .feature-link {
    direction: rtl;
}

.rtl-layout .feature-link i {
    order: -1;
    margin-left: var(--spacing-1);
    margin-right: 0;
}

/* RTL Grid Layouts */
.rtl-layout .hero-container {
    grid-template-columns: 1fr 1fr;
    direction: rtl;
}

.rtl-layout .features-grid {
    direction: rtl;
}

.rtl-layout .templates-grid {
    direction: rtl;
}

.rtl-layout .pricing-grid {
    direction: rtl;
}

/* RTL Forms */
.rtl-layout .form-group {
    direction: rtl;
    text-align: right;
}

.rtl-layout .form-group label {
    text-align: right;
    direction: rtl;
}

.rtl-layout input,
.rtl-layout textarea,
.rtl-layout select {
    direction: rtl;
    text-align: right;
    font-family: var(--arabic-font-primary);
}

.rtl-layout input::placeholder,
.rtl-layout textarea::placeholder {
    direction: rtl;
    text-align: right;
    font-family: var(--arabic-font-primary);
}

/* RTL Dropdowns */
.rtl-layout .dropdown-content {
    right: auto;
    left: 0;
    direction: rtl;
}

.rtl-layout .dropdown-content a {
    direction: rtl;
    text-align: right;
}

.rtl-layout .dropdown-content i {
    margin-left: var(--spacing-2);
    margin-right: 0;
}

/* RTL Modal */
.rtl-layout .modal-content {
    direction: rtl;
    text-align: right;
}

.rtl-layout .auth-form {
    direction: rtl;
    text-align: right;
}

/* RTL Footer */
.rtl-layout .footer-content {
    direction: rtl;
}

.rtl-layout .footer-section {
    text-align: right;
}

.rtl-layout .footer-social {
    justify-content: flex-end;
}

.rtl-layout .footer-links {
    text-align: right;
}

.rtl-layout .footer-contact {
    text-align: right;
    direction: rtl;
}

.rtl-layout .footer-contact i {
    margin-left: var(--spacing-1);
    margin-right: 0;
}

/* Language Switcher */
.language-switcher {
    position: fixed;
    top: 100px;
    left: 20px;
    z-index: 1001;
    background: var(--white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-2);
    display: flex;
    gap: var(--spacing-1);
}

.language-switcher button {
    padding: var(--spacing-1) var(--spacing-2);
    border: 1px solid var(--gray-300);
    background: var(--white);
    border-radius: var(--radius);
    cursor: pointer;
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.language-switcher button.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.language-switcher button:hover {
    background: var(--gray-50);
}

.language-switcher button.active:hover {
    background: var(--primary-dark);
}

/* RTL Responsive Adjustments */
@media (max-width: 768px) {
    .rtl-layout .nav-menu {
        right: auto;
        left: 0;
        direction: rtl;
    }
    
    .rtl-layout .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .rtl-layout .hero-buttons {
        justify-content: center;
    }
    
    .language-switcher {
        left: 10px;
        top: 80px;
    }
}

/* Arabic Number Formatting */
.arabic-numbers {
    font-feature-settings: "lnum" 1;
}

/* Arabic Text Selection */
.rtl-layout ::selection {
    background: var(--primary-color);
    color: var(--white);
}

/* Arabic Scrollbar */
.rtl-layout ::-webkit-scrollbar {
    width: 8px;
}

.rtl-layout ::-webkit-scrollbar-track {
    background: var(--gray-100);
}

.rtl-layout ::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--radius-full);
}

.rtl-layout ::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Form Validation Styles */
.error {
    border-color: var(--error-color) !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

.error-message {
    color: var(--error-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
    font-family: var(--arabic-font-primary);
}

.rtl-layout .error-message {
    direction: rtl;
    text-align: right;
}

.form-error-container {
    position: fixed;
    top: 100px;
    right: 20px;
    z-index: 1002;
    max-width: 400px;
}

.rtl-layout .form-error-container {
    right: auto;
    left: 20px;
}

.form-error-message {
    background: var(--error-color);
    color: var(--white);
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-family: var(--arabic-font-primary);
    animation: slideIn 0.3s ease-out;
}

.form-error-message.rtl {
    direction: rtl;
    text-align: right;
}

.form-error-message .close-error {
    background: none;
    border: none;
    color: var(--white);
    cursor: pointer;
    padding: var(--spacing-1);
    margin-left: auto;
    border-radius: var(--radius);
    transition: var(--transition);
}

.form-error-message .close-error:hover {
    background: rgba(255, 255, 255, 0.2);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.rtl-layout .form-error-message {
    animation: slideInRTL 0.3s ease-out;
}

@keyframes slideInRTL {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Password Strength Indicator */
.password-strength {
    margin-top: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-family: var(--arabic-font-primary);
}

.password-strength.rtl {
    direction: rtl;
    text-align: right;
}

.strength-bar {
    height: 4px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    margin-top: var(--spacing-1);
    overflow: hidden;
}

.strength-fill {
    height: 100%;
    transition: var(--transition);
    border-radius: var(--radius-full);
}

.strength-fill.weak {
    width: 20%;
    background: var(--error-color);
}

.strength-fill.fair {
    width: 40%;
    background: var(--warning-color);
}

.strength-fill.good {
    width: 60%;
    background: var(--accent-color);
}

.strength-fill.strong {
    width: 80%;
    background: var(--success-color);
}

.strength-fill.excellent {
    width: 100%;
    background: var(--success-color);
}

/* Print Styles for Arabic */
@media print {
    .rtl-layout {
        direction: rtl;
        font-family: var(--arabic-font-primary);
    }

    .language-switcher,
    .form-error-container {
        display: none;
    }
}
