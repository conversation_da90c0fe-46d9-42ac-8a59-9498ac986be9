{"name": "elashrafy-cv", "version": "1.0.0", "description": "Professional CV Builder with QR Code and NFC Integration", "main": "index.html", "scripts": {"start": "python -m http.server 8000", "deploy": "node deploy.js", "build": "echo 'No build step required for static site'", "test": "echo 'Testing application...' && node test-deployment.js"}, "keywords": ["cv", "resume", "qr-code", "nfc", "arabic", "professional"], "author": "<PERSON>", "license": "MIT", "homepage": "https://elashrafy-cv.netlify.app", "repository": {"type": "git", "url": "https://github.com/elashrafy/cv-builder.git"}, "engines": {"node": ">=18.0.0"}, "dependencies": {}, "devDependencies": {}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}