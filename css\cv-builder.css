/* CV Builder Styles */

.cv-builder-main {
    padding-top: 80px;
    min-height: 100vh;
    background: var(--gray-50);
}

.cv-builder-container {
    display: flex;
    height: calc(100vh - 80px);
    max-width: 100%;
    margin: 0 auto;
}

/* Sidebar */
.cv-sidebar {
    width: 350px;
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    overflow-y: auto;
    flex-shrink: 0;
    transition: var(--transition);
}

/* RTL Sidebar */
.rtl-layout .cv-sidebar {
    border-right: none;
    border-left: 1px solid var(--gray-200);
    order: 2;
}

.rtl-layout .cv-editor {
    order: 1;
}

.sidebar-header {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sidebar-header h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--radius);
}

.sidebar-toggle:hover {
    background: var(--gray-100);
}

.sidebar-section {
    padding: var(--spacing-6);
    border-bottom: 1px solid var(--gray-100);
}

.sidebar-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

/* Sections List */
.sections-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.section-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition);
    color: var(--gray-600);
}

.section-item:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.section-item.active {
    background: var(--primary-color);
    color: var(--white);
}

.section-item i {
    width: 20px;
    text-align: center;
}

/* AI Suggestions */
.ai-suggestions {
    margin-bottom: var(--spacing-4);
}

.ai-suggestion {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-2);
    padding: var(--spacing-3);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-2);
}

.ai-suggestion i {
    color: var(--accent-color);
    margin-top: 2px;
}

.ai-suggestion p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin: 0;
}

/* Editor */
.cv-editor {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--white);
    overflow: hidden;
}

.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4) var(--spacing-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--white);
}

.editor-tabs {
    display: flex;
    gap: var(--spacing-2);
}

.tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-4);
    background: none;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
}

.tab-btn:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.tab-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.editor-actions {
    display: flex;
    gap: var(--spacing-2);
}

.editor-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-6);
}

/* Editor Sections */
.editor-section {
    display: none;
    margin-bottom: var(--spacing-8);
}

.editor-section.active {
    display: block;
}

.editor-section h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-6);
}

.section-header h3 {
    margin-bottom: 0;
}

/* Form Styles */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.form-group {
    margin-bottom: var(--spacing-4);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-2);
    font-weight: 500;
    color: var(--gray-700);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.form-help {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-2);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.form-help i {
    color: var(--primary-color);
}

/* Photo Upload */
.photo-upload {
    position: relative;
}

.photo-preview {
    width: 150px;
    height: 150px;
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-lg);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    background: var(--gray-50);
}

.photo-preview:hover {
    border-color: var(--primary-color);
    background: var(--primary-color)10;
}

.photo-preview i {
    font-size: var(--font-size-2xl);
    color: var(--gray-400);
    margin-bottom: var(--spacing-2);
}

.photo-preview span {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    text-align: center;
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--radius-lg);
}

/* Skills Input */
.skill-input {
    display: flex;
    gap: var(--spacing-2);
    align-items: flex-end;
}

.skill-input input {
    flex: 1;
}

.skill-input select {
    min-width: 120px;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-4);
}

.skill-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-2) var(--spacing-3);
    background: var(--primary-color)10;
    color: var(--primary-color);
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.skill-level {
    font-size: var(--font-size-xs);
    opacity: 0.8;
}

.skill-remove {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0;
    margin-left: var(--spacing-1);
    opacity: 0.7;
}

.skill-remove:hover {
    opacity: 1;
}

/* Experience/Education Items */
.experience-item,
.education-item,
.project-item,
.certification-item,
.language-item {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-4);
    position: relative;
}

.item-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: var(--spacing-4);
}

.item-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
}

.item-actions {
    display: flex;
    gap: var(--spacing-2);
}

.item-actions .btn {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: var(--font-size-sm);
}

.remove-item {
    position: absolute;
    top: var(--spacing-2);
    right: var(--spacing-2);
    background: var(--error-color);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: var(--font-size-xs);
    transition: var(--transition);
}

.remove-item:hover {
    background: #dc2626;
}

/* CV Preview */
.cv-preview {
    max-width: 800px;
    margin: 0 auto;
    background: var(--white);
    box-shadow: var(--shadow-lg);
    border-radius: var(--radius-lg);
    overflow: hidden;
    min-height: 1000px;
}

.preview-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: var(--gray-500);
}

.preview-loading i {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-3);
    color: var(--primary-color);
}

/* CV Templates */
.cv-template {
    padding: var(--spacing-8);
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-700);
}

.cv-template.modern {
    border-left: 4px solid var(--primary-color);
}

.cv-template.classic {
    border-top: 4px solid var(--accent-color);
}

.cv-template.creative {
    background: linear-gradient(135deg, var(--success-color)05, var(--primary-color)05);
}

.cv-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
    padding-bottom: var(--spacing-6);
    border-bottom: 2px solid var(--gray-200);
}

.cv-name {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.cv-title {
    font-size: var(--font-size-xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-4);
}

.cv-contact {
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.cv-section {
    margin-bottom: var(--spacing-8);
}

.cv-section-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 1px solid var(--gray-300);
}

.cv-summary {
    font-size: var(--font-size-base);
    line-height: 1.7;
    color: var(--gray-700);
}

.cv-experience-item,
.cv-education-item,
.cv-project-item {
    margin-bottom: var(--spacing-6);
}

.cv-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-2);
}

.cv-item-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
}

.cv-item-company {
    font-size: var(--font-size-base);
    color: var(--primary-color);
    font-weight: 500;
}

.cv-item-date {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    font-weight: 500;
}

.cv-item-description {
    color: var(--gray-700);
    margin-top: var(--spacing-2);
}

.cv-skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-4);
}

.cv-skill-category {
    background: var(--gray-50);
    padding: var(--spacing-4);
    border-radius: var(--radius-md);
}

.cv-skill-category h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.cv-skill-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-1);
}

.cv-skill {
    padding: var(--spacing-1) var(--spacing-2);
    background: var(--primary-color);
    color: var(--white);
    border-radius: var(--radius);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

/* Navigation Actions */
.cv-actions {
    display: flex;
    gap: var(--spacing-2);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .cv-sidebar {
        width: 300px;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .cv-builder-container {
        flex-direction: column;
        height: auto;
    }
    
    .cv-sidebar {
        width: 100%;
        height: auto;
        max-height: 300px;
        order: 2;
    }
    
    .cv-editor {
        order: 1;
        min-height: 60vh;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .cv-sidebar.collapsed {
        height: 60px;
        overflow: hidden;
    }
    
    .cv-sidebar.collapsed .sidebar-section {
        display: none;
    }
    
    .editor-header {
        flex-direction: column;
        gap: var(--spacing-3);
        align-items: stretch;
    }
    
    .editor-tabs {
        justify-content: center;
    }
    
    .cv-actions {
        justify-content: center;
    }
    
    .nav-auth {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .cv-actions {
        order: 1;
        width: 100%;
        justify-content: center;
    }
    
    .user-menu {
        order: 2;
    }
}

@media (max-width: 480px) {
    .editor-content {
        padding: var(--spacing-4);
    }
    
    .sidebar-section {
        padding: var(--spacing-4);
    }
    
    .cv-actions .btn {
        flex: 1;
    }
    
    .skill-input {
        flex-direction: column;
        align-items: stretch;
    }
    
    .item-actions {
        flex-direction: column;
    }
}
