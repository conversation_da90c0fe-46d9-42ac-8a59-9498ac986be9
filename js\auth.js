// Authentication Management
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.dbService = window.dbService;
        this.init();
    }

    async init() {
        // Check for existing session
        await this.checkAuthState();
        
        // Listen for auth state changes
        this.dbService.supabase.auth.onAuthStateChange((event, session) => {
            this.handleAuthStateChange(event, session);
        });

        // Setup event listeners
        this.setupEventListeners();
    }

    async checkAuthState() {
        try {
            const { user, error } = await this.dbService.getCurrentUser();
            if (error) throw error;

            if (user) {
                this.currentUser = user;
                await this.loadUserProfile();
                this.updateUI(true);
            } else {
                this.updateUI(false);
            }
        } catch (error) {
            console.error('Auth state check error:', error);
            this.updateUI(false);
        }
    }

    async handleAuthStateChange(event, session) {
        console.log('Auth state changed:', event, session);

        switch (event) {
            case 'SIGNED_IN':
                this.currentUser = session.user;
                await this.loadUserProfile();
                this.updateUI(true);
                this.closeAuthModal();
                this.showSuccessMessage('Successfully signed in!');
                break;

            case 'SIGNED_OUT':
                this.currentUser = null;
                this.userProfile = null;
                this.updateUI(false);
                this.showSuccessMessage('Successfully signed out!');
                // Redirect to home if on protected page
                if (window.location.pathname.includes('/pages/')) {
                    window.location.href = '/';
                }
                break;

            case 'TOKEN_REFRESHED':
                console.log('Token refreshed');
                break;

            default:
                break;
        }
    }

    async loadUserProfile() {
        if (!this.currentUser) return;

        try {
            const { data, error } = await this.dbService.getProfile(this.currentUser.id);
            if (error) throw error;

            this.userProfile = data;
        } catch (error) {
            console.error('Load profile error:', error);
        }
    }

    setupEventListeners() {
        // Modal controls
        const modal = document.getElementById('auth-modal');
        const loginBtn = document.getElementById('login-btn');
        const signupBtn = document.getElementById('signup-btn');
        const closeBtn = document.querySelector('.close');
        const showSignupLink = document.getElementById('show-signup');
        const showLoginLink = document.getElementById('show-login');
        const logoutBtn = document.getElementById('logout-btn');
        const getStartedBtn = document.getElementById('get-started-btn');

        // Open login modal
        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                this.showLoginForm();
                this.openAuthModal();
            });
        }

        // Open signup modal
        if (signupBtn) {
            signupBtn.addEventListener('click', () => {
                this.showSignupForm();
                this.openAuthModal();
            });
        }

        // Get started button
        if (getStartedBtn) {
            getStartedBtn.addEventListener('click', () => {
                if (this.currentUser) {
                    window.location.href = 'pages/dashboard.html';
                } else {
                    this.showSignupForm();
                    this.openAuthModal();
                }
            });
        }

        // Close modal
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeAuthModal();
            });
        }

        // Switch between login and signup
        if (showSignupLink) {
            showSignupLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSignupForm();
            });
        }

        if (showLoginLink) {
            showLoginLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showLoginForm();
            });
        }

        // Logout
        if (logoutBtn) {
            logoutBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                await this.signOut();
            });
        }

        // Close modal when clicking outside
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeAuthModal();
                }
            });
        }

        // Form submissions
        const loginForm = document.querySelector('#login-form form');
        const signupForm = document.querySelector('#signup-form form');

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin(e);
            });
        }

        if (signupForm) {
            signupForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSignup(e);
            });
        }
    }

    async handleLogin(event) {
        const form = event.target;
        const email = form.querySelector('#login-email').value;
        const password = form.querySelector('#login-password').value;
        const submitBtn = form.querySelector('button[type="submit"]');

        if (!email || !password) {
            this.showErrorMessage('Please fill in all fields');
            return;
        }

        try {
            this.setLoading(submitBtn, true);
            const { data, error } = await this.dbService.signIn(email, password);

            if (error) throw error;

            // Success is handled by auth state change
        } catch (error) {
            console.error('Login error:', error);
            this.showErrorMessage(error.message || 'Login failed');
        } finally {
            this.setLoading(submitBtn, false);
        }
    }

    async handleSignup(event) {
        const form = event.target;
        const fullName = form.querySelector('#signup-name').value;
        const email = form.querySelector('#signup-email').value;
        const password = form.querySelector('#signup-password').value;
        const submitBtn = form.querySelector('button[type="submit"]');

        if (!fullName || !email || !password) {
            this.showErrorMessage('Please fill in all fields');
            return;
        }

        if (password.length < 6) {
            this.showErrorMessage('Password must be at least 6 characters');
            return;
        }

        try {
            this.setLoading(submitBtn, true);
            const { data, error } = await this.dbService.signUp(email, password, fullName);

            if (error) throw error;

            this.showSuccessMessage('Account created! Please check your email to verify your account.');
            this.closeAuthModal();
        } catch (error) {
            console.error('Signup error:', error);
            this.showErrorMessage(error.message || 'Signup failed');
        } finally {
            this.setLoading(submitBtn, false);
        }
    }

    async signOut() {
        try {
            const { error } = await this.dbService.signOut();
            if (error) throw error;
        } catch (error) {
            console.error('Signout error:', error);
            this.showErrorMessage('Signout failed');
        }
    }

    openAuthModal() {
        const modal = document.getElementById('auth-modal');
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    }

    closeAuthModal() {
        const modal = document.getElementById('auth-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        this.clearForms();
    }

    showLoginForm() {
        const loginForm = document.getElementById('login-form');
        const signupForm = document.getElementById('signup-form');
        
        if (loginForm && signupForm) {
            loginForm.style.display = 'block';
            signupForm.style.display = 'none';
        }
    }

    showSignupForm() {
        const loginForm = document.getElementById('login-form');
        const signupForm = document.getElementById('signup-form');
        
        if (loginForm && signupForm) {
            loginForm.style.display = 'none';
            signupForm.style.display = 'block';
        }
    }

    clearForms() {
        const forms = document.querySelectorAll('#auth-modal form');
        forms.forEach(form => {
            form.reset();
            // Clear any error states
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => {
                input.classList.remove('error');
            });
        });
        this.clearMessages();
    }

    updateUI(isAuthenticated) {
        const loginBtn = document.getElementById('login-btn');
        const signupBtn = document.getElementById('signup-btn');
        const userMenu = document.getElementById('user-menu');
        const getStartedBtn = document.getElementById('get-started-btn');

        if (isAuthenticated && this.currentUser) {
            // Hide auth buttons
            if (loginBtn) loginBtn.style.display = 'none';
            if (signupBtn) signupBtn.style.display = 'none';

            // Show user menu
            if (userMenu) {
                userMenu.style.display = 'flex';
                
                // Update user info
                const userName = document.getElementById('user-name');
                const userAvatar = document.getElementById('user-avatar');
                
                if (userName) {
                    userName.textContent = this.userProfile?.full_name || this.currentUser.email;
                }
                
                if (userAvatar) {
                    userAvatar.src = this.userProfile?.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(this.userProfile?.full_name || this.currentUser.email)}&background=2563eb&color=fff`;
                }
            }

            // Update get started button
            if (getStartedBtn) {
                getStartedBtn.textContent = 'Go to Dashboard';
            }
        } else {
            // Show auth buttons
            if (loginBtn) loginBtn.style.display = 'inline-flex';
            if (signupBtn) signupBtn.style.display = 'inline-flex';

            // Hide user menu
            if (userMenu) userMenu.style.display = 'none';

            // Update get started button
            if (getStartedBtn) {
                getStartedBtn.textContent = 'Get Started Free';
            }
        }
    }

    setLoading(button, isLoading) {
        if (!button) return;

        if (isLoading) {
            button.disabled = true;
            button.classList.add('loading');
            button.dataset.originalText = button.textContent;
            button.textContent = 'Loading...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            button.textContent = button.dataset.originalText || button.textContent;
        }
    }

    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type = 'info') {
        // Remove existing messages
        this.clearMessages();

        // Create message element
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="message-close">&times;</button>
        `;

        // Add to page
        document.body.appendChild(messageEl);

        // Auto remove after 5 seconds
        setTimeout(() => {
            this.clearMessages();
        }, 5000);

        // Close button
        const closeBtn = messageEl.querySelector('.message-close');
        closeBtn.addEventListener('click', () => {
            this.clearMessages();
        });

        // Add styles if not already added
        if (!document.getElementById('message-styles')) {
            const styles = document.createElement('style');
            styles.id = 'message-styles';
            styles.textContent = `
                .message {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 16px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    z-index: 10000;
                    max-width: 400px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    animation: slideIn 0.3s ease-out;
                }
                .message-error {
                    background: #fef2f2;
                    color: #dc2626;
                    border: 1px solid #fecaca;
                }
                .message-success {
                    background: #f0fdf4;
                    color: #16a34a;
                    border: 1px solid #bbf7d0;
                }
                .message-info {
                    background: #eff6ff;
                    color: #2563eb;
                    border: 1px solid #bfdbfe;
                }
                .message-close {
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    color: inherit;
                    opacity: 0.7;
                    margin-left: auto;
                }
                .message-close:hover {
                    opacity: 1;
                }
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(styles);
        }
    }

    clearMessages() {
        const messages = document.querySelectorAll('.message');
        messages.forEach(msg => msg.remove());
    }

    // Utility methods
    isAuthenticated() {
        return !!this.currentUser;
    }

    getCurrentUser() {
        return this.currentUser;
    }

    getUserProfile() {
        return this.userProfile;
    }

    // Protected route check
    requireAuth() {
        if (!this.isAuthenticated()) {
            this.showErrorMessage('Please login to access this page');
            setTimeout(() => {
                window.location.href = '/';
            }, 2000);
            return false;
        }
        return true;
    }
}

// Initialize auth manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.authManager = new AuthManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
