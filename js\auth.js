// إدارة المصادقة
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.userProfile = null;
        this.dbService = window.dbService;
        this.isInitialized = false;
        console.log('🔐 تهيئة مدير المصادقة...');
        this.init();
    }

    async init() {
        try {
            console.log('🚀 بدء تهيئة نظام المصادقة...');

            // انتظار تحميل خدمة قاعدة البيانات
            await this.waitForDbService();

            // فحص الجلسة الحالية
            await this.checkAuthState();

            // الاستماع لتغييرات حالة المصادقة
            this.dbService.supabase.auth.onAuthStateChange((event, session) => {
                console.log('🔄 تغيير حالة المصادقة:', event);
                this.handleAuthStateChange(event, session);
            });

            // إعداد مستمعي الأحداث
            this.setupEventListeners();

            // فحص صحة الاتصال
            await this.checkConnectionHealth();

            this.isInitialized = true;
            console.log('✅ تم تهيئة نظام المصادقة بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام المصادقة:', error);
        }
    }

    async waitForDbService() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 50; // 5 seconds maximum wait

            const checkService = () => {
                attempts++;
                console.log(`AuthManager: Waiting for dbService... (attempt ${attempts})`);

                if (window.dbService && window.dbService.supabase) {
                    this.dbService = window.dbService;
                    console.log('✅ AuthManager: dbService loaded successfully');
                    resolve();
                } else if (attempts >= maxAttempts) {
                    console.error('❌ AuthManager: Failed to load dbService after 50 attempts');
                    // Try to create a fallback connection
                    this.createFallbackService();
                    resolve();
                } else {
                    setTimeout(checkService, 100);
                }
            };
            checkService();
        });
    }

    createFallbackService() {
        console.log('🔄 AuthManager: Creating fallback service...');
        try {
            if (window.supabase && typeof window.supabase.createClient === 'function') {
                // Create a minimal service if the main one failed to load
                const SUPABASE_URL = 'https://hoyzvlfeyzzqbmhmsypy.supabase.co';
                const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhveXp2bGZleXp6cWJtaG1zeXB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDk4NDQsImV4cCI6MjA2NTA4NTg0NH0.0PTd0lfyPxXQmU1akMISJYU3eAcLPCZzrJxXrKnus0U';

                const supabaseClient = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

                this.dbService = {
                    supabase: supabaseClient,
                    async signIn(email, password) {
                        try {
                            const { data, error } = await supabaseClient.auth.signInWithPassword({
                                email: email.toLowerCase().trim(),
                                password
                            });
                            return { data, error };
                        } catch (error) {
                            return { data: null, error };
                        }
                    },
                    async signUp(email, password, fullName) {
                        try {
                            const { data, error } = await supabaseClient.auth.signUp({
                                email: email.toLowerCase().trim(),
                                password,
                                options: {
                                    data: { full_name: fullName.trim() }
                                }
                            });
                            return { data, error };
                        } catch (error) {
                            return { data: null, error };
                        }
                    },
                    async signOut() {
                        try {
                            const { error } = await supabaseClient.auth.signOut();
                            return { error };
                        } catch (error) {
                            return { error };
                        }
                    },
                    async getCurrentUser() {
                        try {
                            const { data: { user }, error } = await supabaseClient.auth.getUser();
                            return { user, error };
                        } catch (error) {
                            return { user: null, error };
                        }
                    },
                    async getProfile(userId) {
                        try {
                            const { data, error } = await supabaseClient
                                .from('profiles')
                                .select('*')
                                .eq('id', userId)
                                .single();
                            return { data, error };
                        } catch (error) {
                            return { data: null, error };
                        }
                    }
                };
                console.log('✅ AuthManager: Fallback service created successfully');
            } else {
                console.error('❌ AuthManager: Supabase client not available for fallback');
            }
        } catch (error) {
            console.error('❌ AuthManager: Failed to create fallback service:', error);
        }
    }

    async checkConnectionHealth() {
        const statusEl = document.getElementById('auth-status');

        try {
            if (statusEl) {
                statusEl.style.display = 'block';
                statusEl.textContent = 'Connecting...';
                statusEl.style.color = '#666';
            }

            const health = await this.dbService.getServerHealth();
            console.log('Database connection health:', health);

            if (health.status === 'healthy') {
                if (statusEl) {
                    statusEl.textContent = '✅ Ready';
                    statusEl.style.color = '#16a34a';
                    // Hide status after 3 seconds
                    setTimeout(() => {
                        statusEl.style.display = 'none';
                    }, 3000);
                }
            } else {
                if (statusEl) {
                    statusEl.textContent = '❌ Connection issues';
                    statusEl.style.color = '#dc2626';
                }
                this.showErrorMessage('Connection issues detected. Some features may not work properly.');
            }
        } catch (error) {
            console.error('Health check failed:', error);
            if (statusEl) {
                statusEl.textContent = '❌ Connection failed';
                statusEl.style.color = '#dc2626';
            }
        }
    }

    async checkAuthState() {
        try {
            const { user, error } = await this.dbService.getCurrentUser();
            if (error) throw error;

            if (user) {
                this.currentUser = user;
                await this.loadUserProfile();
                this.updateUI(true);
            } else {
                this.updateUI(false);
            }
        } catch (error) {
            console.error('Auth state check error:', error);
            this.updateUI(false);
        }
    }

    async handleAuthStateChange(event, session) {
        console.log('Auth state changed:', event, session);

        switch (event) {
            case 'SIGNED_IN':
                console.log('✅ تم تسجيل الدخول بنجاح');
                this.currentUser = session.user;
                await this.loadUserProfile();
                this.updateUI(true);
                this.closeAuthModal();
                this.showSuccessMessage('تم تسجيل الدخول بنجاح! مرحباً بك');

                // التوجه إلى لوحة التحكم تلقائياً
                setTimeout(() => {
                    if (window.location.pathname === '/' || window.location.pathname === '/index.html') {
                        window.location.href = 'pages/dashboard.html';
                    }
                }, 1500);
                break;

            case 'SIGNED_OUT':
                console.log('👋 تم تسجيل الخروج');
                this.currentUser = null;
                this.userProfile = null;
                this.updateUI(false);
                this.showSuccessMessage('تم تسجيل الخروج بنجاح');

                // العودة إلى الصفحة الرئيسية
                if (window.location.pathname.includes('/pages/')) {
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                }
                break;

            case 'TOKEN_REFRESHED':
                console.log('Token refreshed');
                break;

            default:
                break;
        }
    }

    async loadUserProfile() {
        if (!this.currentUser) return;

        try {
            const { data, error } = await this.dbService.getProfile(this.currentUser.id);
            if (error) throw error;

            this.userProfile = data;
        } catch (error) {
            console.error('Load profile error:', error);
        }
    }

    setupEventListeners() {
        // Modal controls
        const modal = document.getElementById('auth-modal');
        const loginBtn = document.getElementById('login-btn');
        const signupBtn = document.getElementById('signup-btn');
        const closeBtn = document.querySelector('.close');
        const showSignupLink = document.getElementById('show-signup');
        const showLoginLink = document.getElementById('show-login');
        const logoutBtn = document.getElementById('logout-btn');
        const getStartedBtn = document.getElementById('get-started-btn');

        // Open login modal
        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                this.showLoginForm();
                this.openAuthModal();
            });
        }

        // Open signup modal
        if (signupBtn) {
            signupBtn.addEventListener('click', () => {
                this.showSignupForm();
                this.openAuthModal();
            });
        }

        // Get started button
        if (getStartedBtn) {
            getStartedBtn.addEventListener('click', () => {
                if (this.currentUser) {
                    window.location.href = 'pages/dashboard.html';
                } else {
                    this.showSignupForm();
                    this.openAuthModal();
                }
            });
        }

        // Close modal
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                this.closeAuthModal();
            });
        }

        // Switch between login and signup
        if (showSignupLink) {
            showSignupLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showSignupForm();
            });
        }

        if (showLoginLink) {
            showLoginLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showLoginForm();
            });
        }

        // Logout
        if (logoutBtn) {
            logoutBtn.addEventListener('click', async (e) => {
                e.preventDefault();
                await this.signOut();
            });
        }

        // Close modal when clicking outside
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeAuthModal();
                }
            });
        }

        // Form submissions
        const loginForm = document.querySelector('#login-form form');
        const signupForm = document.querySelector('#signup-form form');

        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin(e);
            });
        }

        if (signupForm) {
            signupForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSignup(e);
            });
        }
    }

    async handleLogin(event) {
        const form = event.target;
        const email = form.querySelector('#login-email').value;
        const password = form.querySelector('#login-password').value;
        const submitBtn = form.querySelector('button[type="submit"]');

        console.log('🔑 Login attempt:', { email, hasPassword: !!password });

        // Clear previous error states
        this.clearFieldErrors(form);

        // Basic validation
        if (!email || !password) {
            this.showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        if (!this.isValidEmail(email)) {
            this.setFieldError(form.querySelector('#login-email'), 'يرجى إدخال بريد إلكتروني صحيح');
            return;
        }

        try {
            this.setLoading(submitBtn, true);

            // Check if dbService is available
            if (!this.dbService) {
                throw new Error('خدمة قاعدة البيانات غير متوفرة');
            }

            console.log('🔄 Calling dbService.signIn...');
            const { data, error } = await this.dbService.signIn(email, password);

            console.log('📊 Login result:', { data: !!data, error: error?.message });

            if (error) {
                console.error('❌ Login error:', error);

                // Handle specific validation errors
                const errorMessage = error.message || error;

                if (errorMessage.includes('Invalid login credentials') || errorMessage.includes('Invalid email or password')) {
                    this.showErrorMessage('البريد الإلكتروني أو كلمة المرور غير صحيحة');
                } else if (errorMessage.includes('Email not confirmed')) {
                    this.showErrorMessage('يرجى تأكيد بريدك الإلكتروني أولاً');
                } else if (errorMessage.includes('email')) {
                    this.setFieldError(form.querySelector('#login-email'), errorMessage);
                } else if (errorMessage.includes('password')) {
                    this.setFieldError(form.querySelector('#login-password'), errorMessage);
                } else {
                    this.showErrorMessage(errorMessage);
                }
                return;
            }

            // Success is handled by auth state change
            console.log('✅ Login successful:', data);
            this.showSuccessMessage('تم تسجيل الدخول بنجاح!');

        } catch (error) {
            console.error('💥 Unexpected login error:', error);
            this.showErrorMessage('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
        } finally {
            this.setLoading(submitBtn, false);
        }
    }

    // Email validation helper
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    async handleSignup(event) {
        const form = event.target;
        const fullName = form.querySelector('#signup-name').value;
        const email = form.querySelector('#signup-email').value;
        const password = form.querySelector('#signup-password').value;
        const submitBtn = form.querySelector('button[type="submit"]');

        console.log('📝 Signup attempt:', { fullName, email, hasPassword: !!password });

        // Clear previous error states
        this.clearFieldErrors(form);

        // التحقق من صحة البيانات
        if (!fullName || !email || !password) {
            this.showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        if (password.length < 6) {
            this.setFieldError(form.querySelector('#signup-password'), 'كلمة المرور يجب أن تكون 6 أحرف على الأقل');
            return;
        }

        if (!this.isValidEmail(email)) {
            this.setFieldError(form.querySelector('#signup-email'), 'يرجى إدخال بريد إلكتروني صحيح');
            return;
        }

        try {
            this.setLoading(submitBtn, true);

            // التحقق من توفر خدمة قاعدة البيانات
            if (!this.dbService) {
                throw new Error('خدمة قاعدة البيانات غير متوفرة');
            }

            console.log('🔄 استدعاء dbService.signUp...');
            const { data, error } = await this.dbService.signUp(email, password, fullName);

            console.log('📊 نتيجة التسجيل:', { data: !!data, error: error?.message });

            if (error) {
                console.error('❌ خطأ في التسجيل:', error);

                const errorMessage = error.message || error;

                if (errorMessage.includes('User already registered') || errorMessage.includes('يوجد حساب بهذا البريد')) {
                    this.showErrorMessage('يوجد حساب بهذا البريد الإلكتروني بالفعل. يرجى تسجيل الدخول بدلاً من ذلك.');
                } else if (errorMessage.includes('email') || errorMessage.includes('بريد')) {
                    this.setFieldError(form.querySelector('#signup-email'), errorMessage);
                } else if (errorMessage.includes('password') || errorMessage.includes('كلمة المرور')) {
                    this.setFieldError(form.querySelector('#signup-password'), errorMessage);
                } else {
                    this.showErrorMessage(errorMessage);
                }
                return;
            }

            // نجح التسجيل - عرض رسالة والتبديل إلى تسجيل الدخول
            console.log('✅ تم التسجيل بنجاح:', data);
            this.showSuccessMessage('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول باستخدام بياناتك.');

            // التبديل إلى نموذج تسجيل الدخول وملء البريد الإلكتروني
            this.showLoginForm();
            const loginEmailField = document.querySelector('#login-email');
            if (loginEmailField) {
                loginEmailField.value = email;
            }

            // مسح نموذج التسجيل
            form.reset();

        } catch (error) {
            console.error('💥 Unexpected signup error:', error);
            this.showErrorMessage('An unexpected error occurred. Please try again.');
        } finally {
            this.setLoading(submitBtn, false);
        }
    }

    async signOut() {
        try {
            const { error } = await this.dbService.signOut();
            if (error) throw error;
        } catch (error) {
            console.error('Signout error:', error);
            this.showErrorMessage('Signout failed');
        }
    }

    openAuthModal() {
        const modal = document.getElementById('auth-modal');
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
    }

    closeAuthModal() {
        const modal = document.getElementById('auth-modal');
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        this.clearForms();
    }

    showLoginForm() {
        const loginForm = document.getElementById('login-form');
        const signupForm = document.getElementById('signup-form');

        if (loginForm && signupForm) {
            loginForm.style.display = 'block';
            signupForm.style.display = 'none';
        }

        // Clear any existing messages
        this.clearMessages();
    }

    showSignupForm() {
        const loginForm = document.getElementById('login-form');
        const signupForm = document.getElementById('signup-form');

        if (loginForm && signupForm) {
            loginForm.style.display = 'none';
            signupForm.style.display = 'block';
        }

        // Clear any existing messages
        this.clearMessages();
    }

    clearForms() {
        const forms = document.querySelectorAll('#auth-modal form');
        forms.forEach(form => {
            form.reset();
            // Clear any error states
            const inputs = form.querySelectorAll('input');
            inputs.forEach(input => {
                input.classList.remove('error');
            });
        });
        this.clearMessages();
    }

    updateUI(isAuthenticated) {
        const loginBtn = document.getElementById('login-btn');
        const signupBtn = document.getElementById('signup-btn');
        const userMenu = document.getElementById('user-menu');
        const getStartedBtn = document.getElementById('get-started-btn');

        if (isAuthenticated && this.currentUser) {
            // Hide auth buttons
            if (loginBtn) loginBtn.style.display = 'none';
            if (signupBtn) signupBtn.style.display = 'none';

            // Show user menu
            if (userMenu) {
                userMenu.style.display = 'flex';

                // Update user info
                const userName = document.getElementById('user-name');
                const userAvatar = document.getElementById('user-avatar');

                if (userName) {
                    userName.textContent = this.userProfile?.full_name || this.currentUser.email;
                }

                if (userAvatar) {
                    userAvatar.src = this.userProfile?.avatar_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(this.userProfile?.full_name || this.currentUser.email)}&background=2563eb&color=fff`;
                }
            }

            // Update get started button
            if (getStartedBtn) {
                getStartedBtn.textContent = 'Go to Dashboard';
            }
        } else {
            // Show auth buttons
            if (loginBtn) loginBtn.style.display = 'inline-flex';
            if (signupBtn) signupBtn.style.display = 'inline-flex';

            // Hide user menu
            if (userMenu) userMenu.style.display = 'none';

            // Update get started button
            if (getStartedBtn) {
                getStartedBtn.textContent = 'Get Started Free';
            }
        }
    }

    setLoading(button, isLoading) {
        if (!button) return;

        if (isLoading) {
            button.disabled = true;
            button.classList.add('loading');
            button.dataset.originalText = button.textContent;
            button.textContent = 'Loading...';
        } else {
            button.disabled = false;
            button.classList.remove('loading');
            button.textContent = button.dataset.originalText || button.textContent;
        }
    }

    showErrorMessage(message) {
        this.showMessage(message, 'error');
    }

    showSuccessMessage(message) {
        this.showMessage(message, 'success');
    }

    showMessage(message, type = 'info') {
        // Remove existing messages
        this.clearMessages();

        // Create message element
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.innerHTML = `
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'}"></i>
            <span>${message}</span>
            <button class="message-close">&times;</button>
        `;

        // Add to page
        document.body.appendChild(messageEl);

        // Auto remove after 5 seconds
        setTimeout(() => {
            this.clearMessages();
        }, 5000);

        // Close button
        const closeBtn = messageEl.querySelector('.message-close');
        closeBtn.addEventListener('click', () => {
            this.clearMessages();
        });

        // Add styles if not already added
        if (!document.getElementById('message-styles')) {
            const styles = document.createElement('style');
            styles.id = 'message-styles';
            styles.textContent = `
                .message {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 16px;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    z-index: 10000;
                    max-width: 400px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    animation: slideIn 0.3s ease-out;
                }
                .message-error {
                    background: #fef2f2;
                    color: #dc2626;
                    border: 1px solid #fecaca;
                }
                .message-success {
                    background: #f0fdf4;
                    color: #16a34a;
                    border: 1px solid #bbf7d0;
                }
                .message-info {
                    background: #eff6ff;
                    color: #2563eb;
                    border: 1px solid #bfdbfe;
                }
                .message-close {
                    background: none;
                    border: none;
                    font-size: 18px;
                    cursor: pointer;
                    color: inherit;
                    opacity: 0.7;
                    margin-left: auto;
                }
                .message-close:hover {
                    opacity: 1;
                }
                .field-error {
                    color: #dc2626;
                    font-size: 0.875rem;
                    margin-top: 4px;
                    display: block;
                }
                .form-group input.error {
                    border-color: #dc2626;
                    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
                }
                .form-group input.error:focus {
                    border-color: #dc2626;
                    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.2);
                }
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(styles);
        }
    }

    clearMessages() {
        const messages = document.querySelectorAll('.message');
        messages.forEach(msg => msg.remove());
    }

    // Field error handling methods
    setFieldError(field, message) {
        if (!field) return;

        field.classList.add('error');

        // Remove existing error message
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // Add new error message
        const errorEl = document.createElement('div');
        errorEl.className = 'field-error';
        errorEl.textContent = message;
        field.parentNode.appendChild(errorEl);
    }

    clearFieldErrors(form) {
        if (!form) return;

        const errorFields = form.querySelectorAll('.error');
        errorFields.forEach(field => field.classList.remove('error'));

        const errorMessages = form.querySelectorAll('.field-error');
        errorMessages.forEach(msg => msg.remove());
    }

    // Utility methods
    isAuthenticated() {
        return !!this.currentUser;
    }

    getCurrentUser() {
        return this.currentUser;
    }

    getUserProfile() {
        return this.userProfile;
    }

    // Protected route check
    requireAuth() {
        if (!this.isAuthenticated()) {
            this.showErrorMessage('Please login to access this page');
            setTimeout(() => {
                window.location.href = '/';
            }, 2000);
            return false;
        }
        return true;
    }
}

// Initialize auth manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Add a small delay to ensure all scripts are loaded
    setTimeout(() => {
        console.log('🔐 Initializing AuthManager...');
        window.authManager = new AuthManager();
    }, 100);
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuthManager;
}
