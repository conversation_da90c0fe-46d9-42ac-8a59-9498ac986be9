// Test deployment script for Elashrafy CV
const https = require('https');
const fs = require('fs');

// Test Supabase connection
async function testSupabaseConnection() {
    return new Promise((resolve, reject) => {
        const options = {
            hostname: 'hoyzvlfeyzzqbmhmsypy.supabase.co',
            port: 443,
            path: '/rest/v1/',
            method: 'GET',
            headers: {
                'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhveXp2bGZleXp6cWJtaG1zeXB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDk4NDQsImV4cCI6MjA2NTA4NTg0NH0.0PTd0lfyPxXQmU1akMISJYU3eAcLPCZzrJxXrKnus0U'
            }
        };

        const req = https.request(options, (res) => {
            if (res.statusCode === 200) {
                console.log('✅ Supabase connection successful');
                resolve(true);
            } else {
                console.log('❌ Supabase connection failed:', res.statusCode);
                resolve(false);
            }
        });

        req.on('error', (error) => {
            console.log('❌ Supabase connection error:', error.message);
            resolve(false);
        });

        req.setTimeout(5000, () => {
            console.log('❌ Supabase connection timeout');
            req.destroy();
            resolve(false);
        });

        req.end();
    });
}

// Check if required files exist
function checkRequiredFiles() {
    const requiredFiles = [
        'index.html',
        'js/supabase-config.js',
        'js/auth.js',
        'css/style.css',
        'pages/dashboard.html',
        'pages/cv-builder.html'
    ];

    let allFilesExist = true;

    console.log('📁 Checking required files...');
    requiredFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`✅ ${file}`);
        } else {
            console.log(`❌ ${file} - MISSING`);
            allFilesExist = false;
        }
    });

    return allFilesExist;
}

// Check Supabase configuration
function checkSupabaseConfig() {
    try {
        const configFile = fs.readFileSync('js/supabase-config.js', 'utf8');
        
        if (configFile.includes('hoyzvlfeyzzqbmhmsypy.supabase.co')) {
            console.log('✅ Supabase URL configured correctly');
        } else {
            console.log('❌ Supabase URL not found in configuration');
            return false;
        }

        if (configFile.includes('eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9')) {
            console.log('✅ Supabase API key configured');
        } else {
            console.log('❌ Supabase API key not found');
            return false;
        }

        return true;
    } catch (error) {
        console.log('❌ Error reading Supabase configuration:', error.message);
        return false;
    }
}

async function main() {
    console.log('🧪 Testing deployment readiness...\n');

    // Test 1: Check required files
    const filesOk = checkRequiredFiles();
    console.log('');

    // Test 2: Check Supabase configuration
    console.log('🔧 Checking Supabase configuration...');
    const configOk = checkSupabaseConfig();
    console.log('');

    // Test 3: Test Supabase connection
    console.log('🌐 Testing Supabase connection...');
    const connectionOk = await testSupabaseConnection();
    console.log('');

    // Summary
    console.log('📊 Deployment Readiness Summary:');
    console.log(`Files: ${filesOk ? '✅' : '❌'}`);
    console.log(`Configuration: ${configOk ? '✅' : '❌'}`);
    console.log(`Connection: ${connectionOk ? '✅' : '❌'}`);

    if (filesOk && configOk && connectionOk) {
        console.log('\n🎉 Application is ready for deployment!');
        console.log('🚀 You can deploy to:');
        console.log('   • Netlify: https://app.netlify.com/drop');
        console.log('   • Vercel: https://vercel.com/new');
        console.log('   • GitHub Pages: Push to GitHub repository');
        return true;
    } else {
        console.log('\n❌ Application is not ready for deployment');
        console.log('Please fix the issues above before deploying');
        return false;
    }
}

if (require.main === module) {
    main().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testSupabaseConnection, checkRequiredFiles, checkSupabaseConfig };
