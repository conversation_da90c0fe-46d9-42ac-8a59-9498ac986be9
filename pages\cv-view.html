<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض السيرة الذاتية - Elashrafy CV</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/arabic.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- PDF Export Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
    <style>
        .cv-view-main {
            padding-top: 80px;
            min-height: 100vh;
            background: var(--gray-50);
        }
        
        .cv-view-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: var(--spacing-4) 0;
            position: sticky;
            top: 80px;
            z-index: 10;
        }
        
        .cv-view-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--spacing-4);
        }
        
        .cv-view-info h1 {
            font-size: var(--font-size-xl);
            margin: 0 0 var(--spacing-1) 0;
            color: var(--gray-900);
        }
        
        .cv-view-info p {
            margin: 0;
            color: var(--gray-600);
            font-size: var(--font-size-sm);
        }
        
        .cv-actions {
            display: flex;
            gap: var(--spacing-2);
            align-items: center;
        }
        
        .cv-container {
            max-width: 900px;
            margin: var(--spacing-8) auto;
            background: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
        }
        
        .cv-content {
            padding: var(--spacing-8);
        }
        
        /* CV Template Styles */
        .cv-template {
            font-family: var(--font-family-arabic);
            line-height: 1.6;
            color: var(--gray-900);
        }
        
        .cv-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-6);
            margin-bottom: var(--spacing-8);
            padding-bottom: var(--spacing-6);
            border-bottom: 2px solid var(--gray-100);
        }
        
        .cv-photo {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid var(--primary-color);
        }
        
        .cv-basic-info h1 {
            font-size: var(--font-size-3xl);
            font-weight: 700;
            color: var(--primary-color);
            margin: 0 0 var(--spacing-2) 0;
        }
        
        .cv-basic-info h2 {
            font-size: var(--font-size-xl);
            font-weight: 500;
            color: var(--gray-600);
            margin: 0 0 var(--spacing-4) 0;
        }
        
        .cv-contact-info {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-4);
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            color: var(--gray-700);
            font-size: var(--font-size-sm);
        }
        
        .contact-item i {
            color: var(--primary-color);
            width: 16px;
        }
        
        .cv-section {
            margin-bottom: var(--spacing-8);
        }
        
        .cv-section-title {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: var(--spacing-4);
            padding-bottom: var(--spacing-2);
            border-bottom: 2px solid var(--primary-color);
            position: relative;
        }
        
        .cv-section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            right: 0;
            width: 50px;
            height: 2px;
            background: var(--accent-color);
        }
        
        .cv-summary {
            font-size: var(--font-size-base);
            line-height: 1.8;
            color: var(--gray-700);
        }
        
        .cv-item {
            margin-bottom: var(--spacing-6);
            padding-bottom: var(--spacing-4);
            border-bottom: 1px solid var(--gray-100);
        }
        
        .cv-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .cv-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--spacing-2);
        }
        
        .cv-item-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--gray-900);
            margin: 0;
        }
        
        .cv-item-company {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--primary-color);
            margin: var(--spacing-1) 0;
        }
        
        .cv-item-date {
            font-size: var(--font-size-sm);
            color: var(--gray-500);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .cv-item-description {
            color: var(--gray-700);
            line-height: 1.7;
        }
        
        .cv-skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-4);
        }
        
        .cv-skill-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-2) 0;
        }
        
        .cv-skill-name {
            font-weight: 500;
            color: var(--gray-900);
        }
        
        .cv-skill-level {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
        }
        
        .cv-skill-bar {
            width: 80px;
            height: 6px;
            background: var(--gray-200);
            border-radius: 3px;
            overflow: hidden;
        }
        
        .cv-skill-progress {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
            transition: width 0.3s ease;
        }
        
        .cv-languages-list {
            display: flex;
            flex-wrap: wrap;
            gap: var(--spacing-3);
        }
        
        .cv-language-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
            padding: var(--spacing-2) var(--spacing-4);
            background: var(--gray-100);
            border-radius: var(--radius-full);
            font-size: var(--font-size-sm);
        }
        
        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-16);
            color: var(--gray-500);
        }
        
        .loading-state i {
            font-size: var(--font-size-3xl);
            margin-bottom: var(--spacing-4);
            color: var(--primary-color);
        }
        
        .error-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-16);
            color: var(--error-color);
            text-align: center;
        }
        
        .error-state i {
            font-size: var(--font-size-3xl);
            margin-bottom: var(--spacing-4);
        }
        
        @media (max-width: 768px) {
            .cv-view-actions {
                flex-direction: column;
                align-items: stretch;
                gap: var(--spacing-3);
            }
            
            .cv-actions {
                justify-content: center;
                flex-wrap: wrap;
            }
            
            .cv-header {
                flex-direction: column;
                text-align: center;
            }
            
            .cv-contact-info {
                justify-content: center;
            }
            
            .cv-item-header {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .cv-skills-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media print {
            .cv-view-header,
            .no-print {
                display: none !important;
            }
            
            .cv-view-main {
                padding-top: 0;
            }
            
            .cv-container {
                margin: 0;
                box-shadow: none;
                border-radius: 0;
            }
            
            .cv-content {
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header no-print">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../index.html">
                        <i class="fas fa-file-alt"></i>
                        <span>Elashrafy CV</span>
                    </a>
                </div>
                
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="../index.html" class="nav-link">الرئيسية</a></li>
                        <li><a href="dashboard.html" class="nav-link">لوحة التحكم</a></li>
                        <li><a href="cv-builder.html" class="nav-link">منشئ السيرة الذاتية</a></li>
                        <li><a href="templates.html" class="nav-link">القوالب</a></li>
                        <li><a href="qr-generator.html" class="nav-link">مولد QR</a></li>
                        <li><a href="nfc-manager.html" class="nav-link">إدارة NFC</a></li>
                    </ul>
                </nav>
                
                <div class="header-actions">
                    <div class="language-switcher">
                        <button class="language-btn" onclick="toggleLanguage()">
                            <i class="fas fa-globe"></i>
                            <span id="current-language">العربية</span>
                        </button>
                    </div>
                    
                    <button class="nav-toggle" id="nav-toggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="cv-view-main">
        <!-- CV View Header -->
        <section class="cv-view-header no-print">
            <div class="container">
                <div class="cv-view-actions">
                    <div class="cv-view-info">
                        <h1 id="cv-title">جاري تحميل السيرة الذاتية...</h1>
                        <p id="cv-owner">المالك: غير محدد</p>
                    </div>
                    
                    <div class="cv-actions">
                        <button class="btn btn-outline" onclick="printCV()">
                            <i class="fas fa-print"></i>
                            طباعة
                        </button>
                        <button class="btn btn-outline" onclick="downloadPDF()">
                            <i class="fas fa-file-pdf"></i>
                            تحميل PDF
                        </button>
                        <button class="btn btn-primary" onclick="shareCV()">
                            <i class="fas fa-share-alt"></i>
                            مشاركة
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- CV Container -->
        <div class="container">
            <div class="cv-container">
                <div class="cv-content" id="cv-content">
                    <!-- Loading State -->
                    <div class="loading-state" id="loading-state">
                        <i class="fas fa-spinner fa-spin"></i>
                        <h3>جاري تحميل السيرة الذاتية...</h3>
                        <p>يرجى الانتظار قليلاً</p>
                    </div>
                    
                    <!-- Error State -->
                    <div class="error-state" id="error-state" style="display: none;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>خطأ في تحميل السيرة الذاتية</h3>
                        <p id="error-message">حدث خطأ أثناء تحميل السيرة الذاتية</p>
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="fas fa-refresh"></i>
                            إعادة المحاولة
                        </button>
                    </div>
                    
                    <!-- CV Template will be rendered here -->
                </div>
            </div>
        </div>
    </main>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../js/supabase-config.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/db-service.js"></script>
    <script src="../js/translations.js"></script>
    <script src="../js/arabic-templates.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        // CV Viewer functionality
        class CVViewer {
            constructor() {
                this.cvId = null;
                this.cvData = null;
                this.init();
            }

            init() {
                // Get CV ID from URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                this.cvId = urlParams.get('id');
                
                if (!this.cvId) {
                    this.showError('لم يتم تحديد السيرة الذاتية');
                    return;
                }
                
                this.loadCV();
            }

            async loadCV() {
                try {
                    const { data, error } = await window.dbService.getPublicCV(this.cvId);
                    
                    if (error) {
                        throw new Error(error.message);
                    }
                    
                    if (!data) {
                        throw new Error('السيرة الذاتية غير موجودة أو غير متاحة للعرض العام');
                    }
                    
                    this.cvData = data;
                    this.renderCV();
                    this.updateViewCount();
                    
                } catch (error) {
                    console.error('Error loading CV:', error);
                    this.showError(error.message);
                }
            }

            renderCV() {
                const contentEl = document.getElementById('cv-content');
                const loadingEl = document.getElementById('loading-state');
                const errorEl = document.getElementById('error-state');
                
                // Hide loading and error states
                loadingEl.style.display = 'none';
                errorEl.style.display = 'none';
                
                // Update header info
                document.getElementById('cv-title').textContent = this.cvData.title;
                document.getElementById('cv-owner').textContent = `المالك: ${this.cvData.profiles?.full_name || 'غير محدد'}`;
                
                // Generate CV HTML using Arabic templates
                if (window.arabicTemplates) {
                    const templateHTML = window.arabicTemplates.generateTemplateHTML(
                        this.cvData.cv_data,
                        this.cvData.cv_data.template || 'modern',
                        'ar'
                    );
                    contentEl.innerHTML = templateHTML;
                    
                    // Apply template styles
                    window.arabicTemplates.applyTemplateStyles(this.cvData.cv_data.template || 'modern');
                } else {
                    // Fallback rendering
                    contentEl.innerHTML = this.generateFallbackHTML();
                }
            }

            generateFallbackHTML() {
                const { cv_data } = this.cvData;
                const { personal, summary, experience, education, skills } = cv_data;
                
                return `
                    <div class="cv-template">
                        <div class="cv-header">
                            ${personal.photo ? `<img src="${personal.photo}" alt="Profile Photo" class="cv-photo">` : ''}
                            <div class="cv-basic-info">
                                <h1>${personal.fullName || 'الاسم الكامل'}</h1>
                                <h2>${personal.jobTitle || 'المسمى الوظيفي'}</h2>
                                <div class="cv-contact-info">
                                    ${personal.email ? `<div class="contact-item"><i class="fas fa-envelope"></i> ${personal.email}</div>` : ''}
                                    ${personal.phone ? `<div class="contact-item"><i class="fas fa-phone"></i> ${personal.phone}</div>` : ''}
                                    ${personal.location ? `<div class="contact-item"><i class="fas fa-map-marker-alt"></i> ${personal.location}</div>` : ''}
                                </div>
                            </div>
                        </div>
                        
                        ${summary ? `
                            <div class="cv-section">
                                <h3 class="cv-section-title">الملخص المهني</h3>
                                <div class="cv-summary">${summary}</div>
                            </div>
                        ` : ''}
                        
                        ${experience && experience.length > 0 ? `
                            <div class="cv-section">
                                <h3 class="cv-section-title">الخبرة العملية</h3>
                                ${experience.map(exp => `
                                    <div class="cv-item">
                                        <div class="cv-item-header">
                                            <div>
                                                <h4 class="cv-item-title">${exp.title}</h4>
                                                <div class="cv-item-company">${exp.company}</div>
                                            </div>
                                            <div class="cv-item-date">${this.formatDateRange(exp.startDate, exp.endDate, exp.current)}</div>
                                        </div>
                                        ${exp.description ? `<div class="cv-item-description">${exp.description}</div>` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        ` : ''}
                        
                        ${skills && skills.length > 0 ? `
                            <div class="cv-section">
                                <h3 class="cv-section-title">المهارات</h3>
                                <div class="cv-skills-grid">
                                    ${skills.map(skill => `
                                        <div class="cv-skill-item">
                                            <span class="cv-skill-name">${skill.name}</span>
                                            <div class="cv-skill-level">
                                                <div class="cv-skill-bar">
                                                    <div class="cv-skill-progress" style="width: ${this.getSkillWidth(skill.level)}%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            formatDateRange(startDate, endDate, isCurrent) {
                if (!startDate) return '';
                
                const start = new Date(startDate).toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
                const end = isCurrent ? 'حتى الآن' : new Date(endDate).toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' });
                
                return `${end} - ${start}`;
            }

            getSkillWidth(level) {
                const levels = {
                    beginner: 25,
                    intermediate: 50,
                    advanced: 75,
                    expert: 100
                };
                return levels[level] || 50;
            }

            async updateViewCount() {
                try {
                    await window.dbService.incrementCVViews(this.cvId);
                } catch (error) {
                    console.error('Error updating view count:', error);
                }
            }

            showError(message) {
                const loadingEl = document.getElementById('loading-state');
                const errorEl = document.getElementById('error-state');
                const errorMessageEl = document.getElementById('error-message');
                
                loadingEl.style.display = 'none';
                errorEl.style.display = 'flex';
                errorMessageEl.textContent = message;
            }
        }

        // Global functions
        function printCV() {
            window.print();
        }

        async function downloadPDF() {
            try {
                const element = document.getElementById('cv-content');
                const canvas = await html2canvas(element, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true
                });

                const imgData = canvas.toDataURL('image/png');
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');
                
                const imgWidth = 210;
                const pageHeight = 295;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;
                let position = 0;

                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;

                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }

                const fileName = window.cvViewer.cvData?.title || 'CV';
                pdf.save(`${fileName}.pdf`);
                
            } catch (error) {
                console.error('Error generating PDF:', error);
                alert('حدث خطأ أثناء إنشاء ملف PDF');
            }
        }

        function shareCV() {
            if (navigator.share) {
                navigator.share({
                    title: window.cvViewer.cvData?.title || 'السيرة الذاتية',
                    text: 'شاهد سيرتي الذاتية',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    alert('تم نسخ الرابط إلى الحافظة');
                });
            }
        }

        // Initialize CV viewer
        document.addEventListener('DOMContentLoaded', () => {
            window.cvViewer = new CVViewer();
        });
    </script>
</body>
</html>
