<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Elashrafy CV - منشئ السيرة الذاتية</title>
    <meta name="description" content="منشئ السيرة الذاتية الاحترافي مع دعم رموز QR وبطاقات NFC">
    <meta name="keywords" content="سيرة ذاتية, CV, QR Code, NFC, وظائف, مهن">
    <meta name="author" content="Mohamed Elashrafy">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Elashrafy CV - منشئ السيرة الذاتية">
    <meta property="og:description" content="أنشئ سيرتك الذاتية المهنية مع رموز QR وبطاقات NFC">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://elashrafy-cv.surge.sh">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Supabase -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --background-color: #f8fafc;
            --text-color: #1e293b;
            --border-color: #e2e8f0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: white;
            box-shadow: var(--shadow);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-color);
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: var(--primary-color);
        }

        .auth-buttons {
            display: flex;
            gap: 1rem;
        }

        .btn {
            padding: 0.5rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary-color) 0%, #7c3aed 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .hero .btn {
            font-size: 1.1rem;
            padding: 1rem 2rem;
        }

        /* Features */
        .features {
            padding: 4rem 0;
        }

        .features h2 {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: var(--text-color);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--shadow);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-color);
        }

        .feature-card p {
            color: var(--secondary-color);
        }

        /* Status */
        .status {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--shadow);
            margin: 2rem 0;
            text-align: center;
        }

        .status.success {
            border-right: 4px solid var(--success-color);
        }

        .status.error {
            border-right: 4px solid var(--error-color);
        }

        .status.info {
            border-right: 4px solid var(--primary-color);
        }

        /* Modal */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
        }

        .modal-content {
            background: white;
            margin: 5% auto;
            padding: 2rem;
            border-radius: 1rem;
            max-width: 500px;
            position: relative;
        }

        .close {
            position: absolute;
            top: 1rem;
            left: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--secondary-color);
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border-color);
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Loading */
        .loading {
            opacity: 0.7;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* User Menu */
        .user-menu {
            display: none;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        .user-name {
            font-weight: 500;
        }

        /* Messages */
        .message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: 500;
            z-index: 3000;
            animation: slideIn 0.3s ease-out;
        }

        .message.success {
            background: var(--success-color);
        }

        .message.error {
            background: var(--error-color);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav class="container">
            <div class="logo">
                <i class="fas fa-file-alt"></i>
                Elashrafy CV
            </div>
            
            <ul class="nav-links">
                <li><a href="#home">الرئيسية</a></li>
                <li><a href="#features">المميزات</a></li>
                <li><a href="#templates">القوالب</a></li>
                <li><a href="#contact">اتصل بنا</a></li>
            </ul>
            
            <div class="auth-buttons">
                <button id="login-btn" class="btn btn-outline">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
                <button id="signup-btn" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i>
                    إنشاء حساب
                </button>
                
                <div id="user-menu" class="user-menu">
                    <img id="user-avatar" class="user-avatar" src="" alt="صورة المستخدم">
                    <span id="user-name" class="user-name"></span>
                    <button id="logout-btn" class="btn btn-outline">
                        <i class="fas fa-sign-out-alt"></i>
                        خروج
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <h1>أنشئ سيرتك الذاتية المهنية</h1>
            <p>منصة شاملة لإنشاء السيرة الذاتية مع دعم رموز QR وبطاقات NFC</p>
            <button id="get-started-btn" class="btn btn-primary">
                <i class="fas fa-rocket"></i>
                ابدأ الآن مجاناً
            </button>
        </div>
    </section>

    <!-- Status Section -->
    <section class="container">
        <div id="connection-status" class="status info">
            <i class="fas fa-spinner fa-spin"></i>
            جاري فحص الاتصال بقاعدة البيانات...
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <h2>المميزات الرئيسية</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3>منشئ السيرة الذاتية</h3>
                    <p>أنشئ سيرتك الذاتية بسهولة مع قوالب مهنية متنوعة</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <h3>رموز QR</h3>
                    <p>أنشئ رموز QR لسيرتك الذاتية للمشاركة السريعة</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-credit-card"></i>
                    </div>
                    <h3>بطاقات NFC</h3>
                    <p>إدارة البطاقات الذكية للتواصل المهني</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>تحليلات متقدمة</h3>
                    <p>تتبع مشاهدات سيرتك الذاتية والتفاعل معها</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3>تصميم متجاوب</h3>
                    <p>يعمل بشكل مثالي على جميع الأجهزة</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3>أمان متقدم</h3>
                    <p>حماية بياناتك مع أحدث تقنيات الأمان</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Auth Modal -->
    <div id="auth-modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            
            <!-- Login Form -->
            <div id="login-form">
                <h2>تسجيل الدخول</h2>
                <form>
                    <div class="form-group">
                        <label for="login-email">البريد الإلكتروني</label>
                        <input type="email" id="login-email" required>
                    </div>
                    <div class="form-group">
                        <label for="login-password">كلمة المرور</label>
                        <input type="password" id="login-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        تسجيل الدخول
                    </button>
                </form>
                <p style="text-align: center; margin-top: 1rem;">
                    ليس لديك حساب؟ 
                    <a href="#" id="show-signup" style="color: var(--primary-color);">إنشاء حساب</a>
                </p>
            </div>
            
            <!-- Signup Form -->
            <div id="signup-form" style="display: none;">
                <h2>إنشاء حساب جديد</h2>
                <form>
                    <div class="form-group">
                        <label for="signup-name">الاسم الكامل</label>
                        <input type="text" id="signup-name" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-email">البريد الإلكتروني</label>
                        <input type="email" id="signup-email" required>
                    </div>
                    <div class="form-group">
                        <label for="signup-password">كلمة المرور</label>
                        <input type="password" id="signup-password" required>
                    </div>
                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        إنشاء حساب
                    </button>
                </form>
                <p style="text-align: center; margin-top: 1rem;">
                    لديك حساب بالفعل؟ 
                    <a href="#" id="show-login" style="color: var(--primary-color);">تسجيل الدخول</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Supabase Configuration -->
    <script>
        // إعدادات Supabase
        const SUPABASE_URL = 'https://hoyzvlfeyzzqbmhmsypy.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhveXp2bGZleXp6cWJtaG1zeXB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDk4NDQsImV4cCI6MjA2NTA4NTg0NH0.0PTd0lfyPxXQmU1akMISJYU3eAcLPCZzrJxXrKnus0U';

        // إنشاء عميل Supabase
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

        // متغيرات عامة
        let currentUser = null;

        // فحص الاتصال بقاعدة البيانات
        async function checkConnection() {
            const statusEl = document.getElementById('connection-status');
            
            try {
                const { data, error } = await supabase
                    .from('cv_templates')
                    .select('id')
                    .limit(1);
                
                if (error) throw error;
                
                statusEl.innerHTML = '<i class="fas fa-check-circle"></i> ✅ متصل بقاعدة البيانات - جاهز للاستخدام';
                statusEl.className = 'status success';
            } catch (error) {
                statusEl.innerHTML = '<i class="fas fa-exclamation-triangle"></i> ❌ خطأ في الاتصال بقاعدة البيانات';
                statusEl.className = 'status error';
                console.error('Database connection error:', error);
            }
        }

        // عرض رسالة
        function showMessage(message, type = 'info') {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.textContent = message;
            document.body.appendChild(messageEl);
            
            setTimeout(() => {
                messageEl.remove();
            }, 5000);
        }

        // فتح نافذة المصادقة
        function openAuthModal() {
            document.getElementById('auth-modal').style.display = 'block';
        }

        // إغلاق نافذة المصادقة
        function closeAuthModal() {
            document.getElementById('auth-modal').style.display = 'none';
        }

        // عرض نموذج تسجيل الدخول
        function showLoginForm() {
            document.getElementById('login-form').style.display = 'block';
            document.getElementById('signup-form').style.display = 'none';
        }

        // عرض نموذج التسجيل
        function showSignupForm() {
            document.getElementById('login-form').style.display = 'none';
            document.getElementById('signup-form').style.display = 'block';
        }

        // تحديث واجهة المستخدم
        function updateUI(isAuthenticated) {
            const authButtons = document.querySelector('.auth-buttons');
            const userMenu = document.getElementById('user-menu');
            
            if (isAuthenticated && currentUser) {
                authButtons.style.display = 'none';
                userMenu.style.display = 'flex';
                
                const userName = document.getElementById('user-name');
                const userAvatar = document.getElementById('user-avatar');
                
                userName.textContent = currentUser.email;
                userAvatar.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentUser.email)}&background=2563eb&color=fff`;
            } else {
                authButtons.style.display = 'flex';
                userMenu.style.display = 'none';
            }
        }

        // تسجيل الدخول
        async function signIn(email, password) {
            try {
                const { data, error } = await supabase.auth.signInWithPassword({
                    email,
                    password
                });
                
                if (error) throw error;
                
                currentUser = data.user;
                updateUI(true);
                closeAuthModal();
                showMessage('تم تسجيل الدخول بنجاح!', 'success');
                
                // توجه إلى لوحة التحكم (محاكاة)
                setTimeout(() => {
                    showMessage('مرحباً بك في لوحة التحكم!', 'success');
                }, 1000);
                
            } catch (error) {
                showMessage('خطأ في تسجيل الدخول: ' + error.message, 'error');
            }
        }

        // إنشاء حساب
        async function signUp(email, password, fullName) {
            try {
                const { data, error } = await supabase.auth.signUp({
                    email,
                    password,
                    options: {
                        data: {
                            full_name: fullName
                        }
                    }
                });
                
                if (error) throw error;
                
                showMessage('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.', 'success');
                showLoginForm();
                
                // ملء البريد الإلكتروني في نموذج تسجيل الدخول
                document.getElementById('login-email').value = email;
                
            } catch (error) {
                showMessage('خطأ في إنشاء الحساب: ' + error.message, 'error');
            }
        }

        // تسجيل الخروج
        async function signOut() {
            try {
                const { error } = await supabase.auth.signOut();
                if (error) throw error;
                
                currentUser = null;
                updateUI(false);
                showMessage('تم تسجيل الخروج بنجاح', 'success');
                
            } catch (error) {
                showMessage('خطأ في تسجيل الخروج: ' + error.message, 'error');
            }
        }

        // إعداد مستمعي الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            // فحص الاتصال
            checkConnection();
            
            // أزرار المصادقة
            document.getElementById('login-btn').addEventListener('click', () => {
                showLoginForm();
                openAuthModal();
            });
            
            document.getElementById('signup-btn').addEventListener('click', () => {
                showSignupForm();
                openAuthModal();
            });
            
            document.getElementById('get-started-btn').addEventListener('click', () => {
                if (currentUser) {
                    showMessage('مرحباً بك في لوحة التحكم!', 'success');
                } else {
                    showSignupForm();
                    openAuthModal();
                }
            });
            
            document.getElementById('logout-btn').addEventListener('click', signOut);
            
            // إغلاق النافذة
            document.querySelector('.close').addEventListener('click', closeAuthModal);
            
            // التبديل بين النماذج
            document.getElementById('show-signup').addEventListener('click', (e) => {
                e.preventDefault();
                showSignupForm();
            });
            
            document.getElementById('show-login').addEventListener('click', (e) => {
                e.preventDefault();
                showLoginForm();
            });
            
            // نماذج المصادقة
            document.querySelector('#login-form form').addEventListener('submit', (e) => {
                e.preventDefault();
                const email = document.getElementById('login-email').value;
                const password = document.getElementById('login-password').value;
                signIn(email, password);
            });
            
            document.querySelector('#signup-form form').addEventListener('submit', (e) => {
                e.preventDefault();
                const name = document.getElementById('signup-name').value;
                const email = document.getElementById('signup-email').value;
                const password = document.getElementById('signup-password').value;
                signUp(email, password, name);
            });
            
            // إغلاق النافذة عند النقر خارجها
            window.addEventListener('click', (e) => {
                if (e.target === document.getElementById('auth-modal')) {
                    closeAuthModal();
                }
            });
            
            // فحص الجلسة الحالية
            supabase.auth.getSession().then(({ data: { session } }) => {
                if (session) {
                    currentUser = session.user;
                    updateUI(true);
                }
            });
            
            // الاستماع لتغييرات المصادقة
            supabase.auth.onAuthStateChange((event, session) => {
                if (event === 'SIGNED_IN') {
                    currentUser = session.user;
                    updateUI(true);
                } else if (event === 'SIGNED_OUT') {
                    currentUser = null;
                    updateUI(false);
                }
            });
        });
    </script>
</body>
</html>
