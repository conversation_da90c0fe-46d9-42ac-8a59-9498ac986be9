// Database Service for Supabase operations
class DatabaseService {
    constructor() {
        this.supabase = null;
        this.init();
    }

    init() {
        // Wait for Supabase to be available
        const checkSupabase = () => {
            if (window.supabase) {
                this.supabase = window.supabase;
                console.log('Database service initialized');
            } else {
                setTimeout(checkSupabase, 100);
            }
        };
        checkSupabase();
    }

    // CV operations
    async createCV(cvData) {
        return await this.supabase
            .from('cvs')
            .insert([cvData])
            .select()
            .single();
    }

    async updateCV(cvId, cvData) {
        return await this.supabase
            .from('cvs')
            .update(cvData)
            .eq('id', cvId)
            .select()
            .single();
    }

    async deleteCV(cvId) {
        return await this.supabase
            .from('cvs')
            .delete()
            .eq('id', cvId);
    }

    async getCV(cvId) {
        return await this.supabase
            .from('cvs')
            .select('*')
            .eq('id', cvId)
            .single();
    }

    async getUserCVs(userId) {
        return await this.supabase
            .from('cvs')
            .select(`
                *,
                cv_templates (
                    name,
                    description
                )
            `)
            .eq('user_id', userId)
            .order('updated_at', { ascending: false });
    }

    // Profile operations
    async createProfile(profileData) {
        return await this.supabase
            .from('profiles')
            .insert([profileData])
            .select()
            .single();
    }

    async updateProfile(userId, profileData) {
        return await this.supabase
            .from('profiles')
            .update(profileData)
            .eq('id', userId)
            .select()
            .single();
    }

    async getProfile(userId) {
        return await this.supabase
            .from('profiles')
            .select('*')
            .eq('id', userId)
            .single();
    }

    // Templates methods
    async getTemplates() {
        return await this.supabase
            .from('cv_templates')
            .select('*')
            .eq('is_active', true);
    }

    async getTemplate(templateId) {
        return await this.supabase
            .from('cv_templates')
            .select('*')
            .eq('id', templateId)
            .single();
    }

    // QR Codes methods
    async createQRCode(qrData) {
        return await this.supabase
            .from('qr_codes')
            .insert([qrData]);
    }

    async getUserQRCodes(userId) {
        return await this.supabase
            .from('qr_codes')
            .select(`
                *,
                cvs (
                    id,
                    title
                )
            `)
            .eq('user_id', userId)
            .order('created_at', { ascending: false });
    }

    async deleteQRCode(qrId) {
        return await this.supabase
            .from('qr_codes')
            .delete()
            .eq('id', qrId);
    }

    // NFC Cards methods
    async createNFCCard(nfcData) {
        return await this.supabase
            .from('nfc_cards')
            .insert([nfcData]);
    }

    async getUserNFCCards(userId) {
        return await this.supabase
            .from('nfc_cards')
            .select(`
                *,
                cvs (
                    id,
                    title
                )
            `)
            .eq('user_id', userId)
            .order('created_at', { ascending: false });
    }

    async updateNFCCard(nfcId, updates) {
        return await this.supabase
            .from('nfc_cards')
            .update(updates)
            .eq('id', nfcId);
    }

    async deleteNFCCard(nfcId) {
        return await this.supabase
            .from('nfc_cards')
            .delete()
            .eq('id', nfcId);
    }

    // Public CV viewing
    async getPublicCV(cvId) {
        return await this.supabase
            .from('cvs')
            .select(`
                *,
                profiles (
                    full_name,
                    avatar_url
                )
            `)
            .eq('id', cvId)
            .eq('is_public', true)
            .single();
    }

    async incrementCVViews(cvId) {
        // First get current views
        const { data: cv } = await this.supabase
            .from('cvs')
            .select('views')
            .eq('id', cvId)
            .single();

        if (cv) {
            return await this.supabase
                .from('cvs')
                .update({ views: (cv.views || 0) + 1 })
                .eq('id', cvId);
        }
    }

    // File upload operations
    async uploadFile(bucket, path, file) {
        return await this.supabase.storage
            .from(bucket)
            .upload(path, file);
    }

    async deleteFile(bucket, path) {
        return await this.supabase.storage
            .from(bucket)
            .remove([path]);
    }

    async getPublicUrl(bucket, path) {
        return this.supabase.storage
            .from(bucket)
            .getPublicUrl(path);
    }

    // Real-time subscriptions
    subscribeToUserCVs(userId, callback) {
        return this.supabase
            .channel('user-cvs')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'cvs',
                filter: `user_id=eq.${userId}`
            }, callback)
            .subscribe();
    }

    subscribeToUserQRCodes(userId, callback) {
        return this.supabase
            .channel('user-qr-codes')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'qr_codes',
                filter: `user_id=eq.${userId}`
            }, callback)
            .subscribe();
    }

    subscribeToUserNFCCards(userId, callback) {
        return this.supabase
            .channel('user-nfc-cards')
            .on('postgres_changes', {
                event: '*',
                schema: 'public',
                table: 'nfc_cards',
                filter: `user_id=eq.${userId}`
            }, callback)
            .subscribe();
    }

    unsubscribe(subscription) {
        if (subscription) {
            this.supabase.removeChannel(subscription);
        }
    }

    // Analytics and statistics
    async getCVStats(cvId) {
        return await this.supabase
            .from('cv_analytics')
            .select('*')
            .eq('cv_id', cvId);
    }

    async recordCVView(cvId, viewerData = {}) {
        return await this.supabase
            .from('cv_analytics')
            .insert([{
                cv_id: cvId,
                event_type: 'view',
                event_data: viewerData,
                created_at: new Date().toISOString()
            }]);
    }

    // Search and filtering
    async searchCVs(userId, searchTerm) {
        return await this.supabase
            .from('cvs')
            .select('*')
            .eq('user_id', userId)
            .ilike('title', `%${searchTerm}%`)
            .order('updated_at', { ascending: false });
    }

    async getCVsByTemplate(userId, templateId) {
        return await this.supabase
            .from('cvs')
            .select('*')
            .eq('user_id', userId)
            .eq('template_id', templateId)
            .order('updated_at', { ascending: false });
    }

    // Backup and export
    async exportUserData(userId) {
        const [cvs, qrCodes, nfcCards, profile] = await Promise.all([
            this.getUserCVs(userId),
            this.getUserQRCodes(userId),
            this.getUserNFCCards(userId),
            this.getProfile(userId)
        ]);

        return {
            profile: profile.data,
            cvs: cvs.data,
            qrCodes: qrCodes.data,
            nfcCards: nfcCards.data,
            exportDate: new Date().toISOString()
        };
    }

    // Utility methods
    async checkConnection() {
        try {
            const { data, error } = await this.supabase
                .from('profiles')
                .select('id')
                .limit(1);
            
            return { connected: !error, error };
        } catch (error) {
            return { connected: false, error };
        }
    }

    formatError(error) {
        if (error?.message) {
            return error.message;
        }
        return 'حدث خطأ غير متوقع';
    }
}

// Initialize database service
document.addEventListener('DOMContentLoaded', () => {
    window.dbService = new DatabaseService();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DatabaseService;
}
