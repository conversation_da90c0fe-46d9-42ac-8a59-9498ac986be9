-- Elashrafy CV Database Setup
-- Run this SQL in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    avatar_url TEXT,
    phone TEXT,
    location TEXT,
    bio TEXT,
    website TEXT,
    linkedin TEXT,
    github TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cv_templates table
CREATE TABLE IF NOT EXISTS cv_templates (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    name_ar TEXT NOT NULL,
    description TEXT,
    description_ar TEXT,
    category TEXT NOT NULL DEFAULT 'modern',
    template_data JSONB NOT NULL DEFAULT '{}',
    preview_image TEXT,
    is_active BOOLEAN DEFAULT true,
    is_premium BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cvs table
CREATE TABLE IF NOT EXISTS cvs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    cv_data JSONB NOT NULL DEFAULT '{}',
    template_id UUID REFERENCES cv_templates(id),
    is_public BOOLEAN DEFAULT false,
    views INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create qr_codes table
CREATE TABLE IF NOT EXISTS qr_codes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    qr_url TEXT NOT NULL,
    qr_image TEXT,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create nfc_cards table
CREATE TABLE IF NOT EXISTS nfc_cards (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    card_name TEXT NOT NULL,
    description TEXT,
    nfc_url TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cv_analytics table for tracking views and interactions
CREATE TABLE IF NOT EXISTS cv_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    cv_id UUID REFERENCES cvs ON DELETE CASCADE NOT NULL,
    event_type TEXT NOT NULL, -- 'view', 'download', 'share'
    event_data JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cvs_user_id ON cvs(user_id);
CREATE INDEX IF NOT EXISTS idx_cvs_is_public ON cvs(is_public);
CREATE INDEX IF NOT EXISTS idx_cvs_created_at ON cvs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_qr_codes_user_id ON qr_codes(user_id);
CREATE INDEX IF NOT EXISTS idx_nfc_cards_user_id ON nfc_cards(user_id);
CREATE INDEX IF NOT EXISTS idx_cv_analytics_cv_id ON cv_analytics(cv_id);
CREATE INDEX IF NOT EXISTS idx_cv_analytics_created_at ON cv_analytics(created_at DESC);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE cvs ENABLE ROW LEVEL SECURITY;
ALTER TABLE qr_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE nfc_cards ENABLE ROW LEVEL SECURITY;
ALTER TABLE cv_analytics ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles 
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles 
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

-- CVs policies
CREATE POLICY "Users can view own CVs" ON cvs 
    FOR SELECT USING (auth.uid() = user_id OR is_public = true);

CREATE POLICY "Users can insert own CVs" ON cvs 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own CVs" ON cvs 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own CVs" ON cvs 
    FOR DELETE USING (auth.uid() = user_id);

-- QR codes policies
CREATE POLICY "Users can view own QR codes" ON qr_codes 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own QR codes" ON qr_codes 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own QR codes" ON qr_codes 
    FOR DELETE USING (auth.uid() = user_id);

-- NFC cards policies
CREATE POLICY "Users can view own NFC cards" ON nfc_cards 
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own NFC cards" ON nfc_cards 
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own NFC cards" ON nfc_cards 
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own NFC cards" ON nfc_cards 
    FOR DELETE USING (auth.uid() = user_id);

-- CV analytics policies
CREATE POLICY "Users can view analytics for own CVs" ON cv_analytics 
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM cvs 
            WHERE cvs.id = cv_analytics.cv_id 
            AND cvs.user_id = auth.uid()
        )
    );

CREATE POLICY "Anyone can insert analytics" ON cv_analytics 
    FOR INSERT WITH CHECK (true);

-- CV templates are public for reading
CREATE POLICY "Templates are public" ON cv_templates 
    FOR SELECT USING (is_active = true);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_profiles_updated_at 
    BEFORE UPDATE ON profiles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cvs_updated_at 
    BEFORE UPDATE ON cvs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cv_templates_updated_at 
    BEFORE UPDATE ON cv_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_nfc_cards_updated_at 
    BEFORE UPDATE ON nfc_cards 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default CV templates
INSERT INTO cv_templates (name, name_ar, description, description_ar, category, template_data, is_active) VALUES
(
    'Modern Professional',
    'مهني حديث',
    'Clean and modern design perfect for tech professionals',
    'تصميم نظيف وحديث مثالي للمهنيين التقنيين',
    'modern',
    '{"colors": {"primary": "#2563eb", "secondary": "#64748b"}, "fonts": {"primary": "Cairo", "secondary": "Inter"}}',
    true
),
(
    'Classic Executive',
    'تنفيذي كلاسيكي',
    'Traditional layout ideal for executive positions',
    'تخطيط تقليدي مثالي للمناصب التنفيذية',
    'classic',
    '{"colors": {"primary": "#f59e0b", "secondary": "#6b7280"}, "fonts": {"primary": "Noto Sans Arabic", "secondary": "Georgia"}}',
    true
),
(
    'Creative Designer',
    'مصمم إبداعي',
    'Bold and creative design for creative professionals',
    'تصميم جريء وإبداعي للمهنيين المبدعين',
    'creative',
    '{"colors": {"primary": "#10b981", "secondary": "#8b5cf6"}, "fonts": {"primary": "Cairo", "secondary": "Poppins"}}',
    true
),
(
    'Minimal Clean',
    'بسيط نظيف',
    'Minimalist design focusing on content',
    'تصميم بسيط يركز على المحتوى',
    'minimal',
    '{"colors": {"primary": "#1f2937", "secondary": "#9ca3af"}, "fonts": {"primary": "Inter", "secondary": "Cairo"}}',
    true
),
(
    'Professional Corporate',
    'مؤسسي مهني',
    'Corporate style perfect for business professionals',
    'أسلوب مؤسسي مثالي للمهنيين في الأعمال',
    'professional',
    '{"colors": {"primary": "#1e40af", "secondary": "#475569"}, "fonts": {"primary": "Cairo", "secondary": "Roboto"}}',
    true
);

-- Create storage buckets (run these in Supabase Dashboard -> Storage)
-- INSERT INTO storage.buckets (id, name, public) VALUES ('avatars', 'avatars', true);
-- INSERT INTO storage.buckets (id, name, public) VALUES ('cv-photos', 'cv-photos', true);
-- INSERT INTO storage.buckets (id, name, public) VALUES ('qr-codes', 'qr-codes', true);

-- Storage policies (uncomment and run if using Supabase Storage)
-- CREATE POLICY "Avatar images are publicly accessible" ON storage.objects 
--     FOR SELECT USING (bucket_id = 'avatars');

-- CREATE POLICY "Users can upload their own avatar" ON storage.objects 
--     FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

-- CREATE POLICY "Users can update their own avatar" ON storage.objects 
--     FOR UPDATE USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

-- CREATE POLICY "Users can delete their own avatar" ON storage.objects 
--     FOR DELETE USING (bucket_id = 'avatars' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Similar policies for cv-photos and qr-codes buckets...

-- Function to handle user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to increment CV views
CREATE OR REPLACE FUNCTION increment_cv_views(cv_uuid UUID)
RETURNS void AS $$
BEGIN
    UPDATE cvs SET views = views + 1 WHERE id = cv_uuid;
    
    INSERT INTO cv_analytics (cv_id, event_type, event_data)
    VALUES (cv_uuid, 'view', '{"timestamp": "' || NOW() || '"}');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user statistics
CREATE OR REPLACE FUNCTION get_user_stats(user_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_cvs', (SELECT COUNT(*) FROM cvs WHERE user_id = user_uuid),
        'total_views', (SELECT COALESCE(SUM(views), 0) FROM cvs WHERE user_id = user_uuid),
        'total_qr_codes', (SELECT COUNT(*) FROM qr_codes WHERE user_id = user_uuid),
        'total_nfc_cards', (SELECT COUNT(*) FROM nfc_cards WHERE user_id = user_uuid),
        'active_nfc_cards', (SELECT COUNT(*) FROM nfc_cards WHERE user_id = user_uuid AND is_active = true)
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION increment_cv_views(UUID) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_user_stats(UUID) TO authenticated;

-- Create a view for public CVs (optional)
CREATE OR REPLACE VIEW public_cvs AS
SELECT 
    c.id,
    c.title,
    c.cv_data,
    c.views,
    c.created_at,
    c.updated_at,
    p.full_name as owner_name,
    p.avatar_url as owner_avatar,
    t.name as template_name
FROM cvs c
LEFT JOIN profiles p ON c.user_id = p.id
LEFT JOIN cv_templates t ON c.template_id = t.id
WHERE c.is_public = true;

-- Grant access to the view
GRANT SELECT ON public_cvs TO anon, authenticated;
