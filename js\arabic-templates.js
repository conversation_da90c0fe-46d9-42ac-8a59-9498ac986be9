// Arabic CV Templates System
class ArabicTemplateManager {
    constructor() {
        this.currentLanguage = 'ar';
        this.isRTL = true;
        this.templates = {
            modern: {
                name: 'مهني حديث',
                nameEn: 'Modern Professional',
                description: 'تصميم نظيف وحديث مثالي للمهنيين التقنيين',
                descriptionEn: 'Clean and modern design perfect for tech professionals',
                colors: {
                    primary: '#2563eb',
                    secondary: '#64748b',
                    accent: '#f59e0b'
                }
            },
            classic: {
                name: 'تنفيذي كلاسيكي',
                nameEn: 'Classic Executive',
                description: 'تخطيط تقليدي مثالي للمناصب التنفيذية',
                descriptionEn: 'Traditional layout ideal for executive positions',
                colors: {
                    primary: '#1f2937',
                    secondary: '#6b7280',
                    accent: '#059669'
                }
            },
            creative: {
                name: 'مصمم إبداعي',
                nameEn: 'Creative Designer',
                description: 'تصميم جريء وإبداعي للمهنيين المبدعين',
                descriptionEn: 'Bold and creative design for creative professionals',
                colors: {
                    primary: '#7c3aed',
                    secondary: '#a78bfa',
                    accent: '#f59e0b'
                }
            }
        };
        this.init();
    }

    init() {
        // Listen for language changes
        window.addEventListener('languageChanged', (e) => {
            this.currentLanguage = e.detail.language;
            this.isRTL = this.currentLanguage === 'ar';
        });
    }

    generatePreview(templateId, cvData) {
        const template = this.templates[templateId];
        if (!template) return '';

        switch (templateId) {
            case 'modern':
                return this.generateModernPreview(cvData, template);
            case 'classic':
                return this.generateClassicPreview(cvData, template);
            case 'creative':
                return this.generateCreativePreview(cvData, template);
            default:
                return this.generateModernPreview(cvData, template);
        }
    }

    generateModernPreview(cvData, template) {
        const personal = cvData.personal || {};
        const isRTL = this.isRTL;
        
        return `
            <div class="cv-template modern ${isRTL ? 'rtl' : 'ltr'}" style="direction: ${isRTL ? 'rtl' : 'ltr'};">
                <!-- Header Section -->
                <div class="cv-header" style="border-left: ${isRTL ? 'none' : '4px solid ' + template.colors.primary}; border-right: ${isRTL ? '4px solid ' + template.colors.primary : 'none'}; padding: 2rem;">
                    ${personal.photo ? `<div class="cv-photo" style="text-align: ${isRTL ? 'right' : 'left'}; margin-bottom: 1rem;"><img src="${personal.photo}" alt="Profile" style="width: 120px; height: 120px; border-radius: 50%; object-fit: cover;"></div>` : ''}
                    <h1 class="cv-name" style="color: ${template.colors.primary}; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                        ${personal.fullName || (isRTL ? 'الاسم الكامل' : 'Full Name')}
                    </h1>
                    <h2 class="cv-title" style="color: ${template.colors.secondary}; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                        ${personal.jobTitle || (isRTL ? 'المسمى الوظيفي' : 'Job Title')}
                    </h2>
                    <div class="cv-contact" style="margin-top: 1rem; text-align: ${isRTL ? 'right' : 'left'}; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'};">
                        ${personal.email ? `<div style="margin-bottom: 0.5rem;"><i class="fas fa-envelope" style="color: ${template.colors.primary}; margin-${isRTL ? 'left' : 'right'}: 0.5rem;"></i>${personal.email}</div>` : ''}
                        ${personal.phone ? `<div style="margin-bottom: 0.5rem;"><i class="fas fa-phone" style="color: ${template.colors.primary}; margin-${isRTL ? 'left' : 'right'}: 0.5rem;"></i>${personal.phone}</div>` : ''}
                        ${personal.location ? `<div style="margin-bottom: 0.5rem;"><i class="fas fa-map-marker-alt" style="color: ${template.colors.primary}; margin-${isRTL ? 'left' : 'right'}: 0.5rem;"></i>${personal.location}</div>` : ''}
                    </div>
                </div>

                <!-- Professional Summary -->
                ${cvData.summary ? this.generateSummarySection(cvData.summary, template, isRTL) : ''}

                <!-- Work Experience -->
                ${cvData.experience && cvData.experience.length > 0 ? this.generateExperienceSection(cvData.experience, template, isRTL) : ''}

                <!-- Education -->
                ${cvData.education && cvData.education.length > 0 ? this.generateEducationSection(cvData.education, template, isRTL) : ''}

                <!-- Skills -->
                ${cvData.skills && cvData.skills.length > 0 ? this.generateSkillsSection(cvData.skills, template, isRTL) : ''}

                <!-- Projects -->
                ${cvData.projects && cvData.projects.length > 0 ? this.generateProjectsSection(cvData.projects, template, isRTL) : ''}

                <!-- Languages -->
                ${cvData.languages && cvData.languages.length > 0 ? this.generateLanguagesSection(cvData.languages, template, isRTL) : ''}
            </div>
        `;
    }

    generateClassicPreview(cvData, template) {
        // Similar structure but with classic styling
        const personal = cvData.personal || {};
        const isRTL = this.isRTL;
        
        return `
            <div class="cv-template classic ${isRTL ? 'rtl' : 'ltr'}" style="direction: ${isRTL ? 'rtl' : 'ltr'}; border-top: 4px solid ${template.colors.primary};">
                <div class="cv-header" style="text-align: center; padding: 2rem; border-bottom: 2px solid #e5e7eb;">
                    ${personal.photo ? `<div class="cv-photo" style="margin-bottom: 1rem;"><img src="${personal.photo}" alt="Profile" style="width: 100px; height: 100px; border-radius: 50%; object-fit: cover;"></div>` : ''}
                    <h1 class="cv-name" style="color: ${template.colors.primary}; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'};">
                        ${personal.fullName || (isRTL ? 'الاسم الكامل' : 'Full Name')}
                    </h1>
                    <h2 class="cv-title" style="color: ${template.colors.secondary}; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'};">
                        ${personal.jobTitle || (isRTL ? 'المسمى الوظيفي' : 'Job Title')}
                    </h2>
                    <div class="cv-contact" style="margin-top: 1rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'};">
                        ${this.generateContactInfo(personal, template, false)}
                    </div>
                </div>
                <div style="padding: 2rem;">
                    ${cvData.summary ? this.generateSummarySection(cvData.summary, template, isRTL) : ''}
                    ${cvData.experience && cvData.experience.length > 0 ? this.generateExperienceSection(cvData.experience, template, isRTL) : ''}
                    ${cvData.education && cvData.education.length > 0 ? this.generateEducationSection(cvData.education, template, isRTL) : ''}
                    ${cvData.skills && cvData.skills.length > 0 ? this.generateSkillsSection(cvData.skills, template, isRTL) : ''}
                </div>
            </div>
        `;
    }

    generateCreativePreview(cvData, template) {
        // Creative template with gradient background
        const personal = cvData.personal || {};
        const isRTL = this.isRTL;
        
        return `
            <div class="cv-template creative ${isRTL ? 'rtl' : 'ltr'}" style="direction: ${isRTL ? 'rtl' : 'ltr'}; background: linear-gradient(135deg, ${template.colors.primary}10, ${template.colors.accent}10);">
                <div class="cv-header" style="background: linear-gradient(135deg, ${template.colors.primary}, ${template.colors.secondary}); color: white; padding: 2rem; text-align: ${isRTL ? 'right' : 'left'};">
                    <div style="display: flex; align-items: center; gap: 2rem; ${isRTL ? 'flex-direction: row-reverse;' : ''}">
                        ${personal.photo ? `<div class="cv-photo"><img src="${personal.photo}" alt="Profile" style="width: 120px; height: 120px; border-radius: 50%; object-fit: cover; border: 4px solid white;"></div>` : ''}
                        <div style="flex: 1;">
                            <h1 class="cv-name" style="color: white; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; margin-bottom: 0.5rem;">
                                ${personal.fullName || (isRTL ? 'الاسم الكامل' : 'Full Name')}
                            </h1>
                            <h2 class="cv-title" style="color: ${template.colors.accent}; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; margin-bottom: 1rem;">
                                ${personal.jobTitle || (isRTL ? 'المسمى الوظيفي' : 'Job Title')}
                            </h2>
                            <div class="cv-contact" style="font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'};">
                                ${this.generateContactInfo(personal, template, true)}
                            </div>
                        </div>
                    </div>
                </div>
                <div style="padding: 2rem;">
                    ${cvData.summary ? this.generateSummarySection(cvData.summary, template, isRTL) : ''}
                    ${cvData.experience && cvData.experience.length > 0 ? this.generateExperienceSection(cvData.experience, template, isRTL) : ''}
                    ${cvData.education && cvData.education.length > 0 ? this.generateEducationSection(cvData.education, template, isRTL) : ''}
                    ${cvData.skills && cvData.skills.length > 0 ? this.generateSkillsSection(cvData.skills, template, isRTL) : ''}
                </div>
            </div>
        `;
    }

    generateContactInfo(personal, template, isLight = false) {
        const iconColor = isLight ? 'white' : template.colors.primary;
        const textColor = isLight ? 'white' : 'inherit';
        const isRTL = this.isRTL;
        
        let contactHtml = '';
        if (personal.email) {
            contactHtml += `<span style="color: ${textColor}; margin-${isRTL ? 'left' : 'right'}: 1rem;"><i class="fas fa-envelope" style="color: ${iconColor}; margin-${isRTL ? 'left' : 'right'}: 0.5rem;"></i>${personal.email}</span>`;
        }
        if (personal.phone) {
            contactHtml += `<span style="color: ${textColor}; margin-${isRTL ? 'left' : 'right'}: 1rem;"><i class="fas fa-phone" style="color: ${iconColor}; margin-${isRTL ? 'left' : 'right'}: 0.5rem;"></i>${personal.phone}</span>`;
        }
        if (personal.location) {
            contactHtml += `<span style="color: ${textColor}; margin-${isRTL ? 'left' : 'right'}: 1rem;"><i class="fas fa-map-marker-alt" style="color: ${iconColor}; margin-${isRTL ? 'left' : 'right'}: 0.5rem;"></i>${personal.location}</span>`;
        }
        
        return contactHtml;
    }

    generateSummarySection(summary, template, isRTL) {
        return `
            <div class="cv-section" style="margin-bottom: 2rem;">
                <h3 style="color: ${template.colors.primary}; border-bottom: 2px solid ${template.colors.primary}; padding-bottom: 0.5rem; margin-bottom: 1rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                    ${isRTL ? 'الملخص المهني' : 'Professional Summary'}
                </h3>
                <p style="line-height: 1.6; color: #374151; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                    ${summary}
                </p>
            </div>
        `;
    }

    generateExperienceSection(experiences, template, isRTL) {
        let experienceHtml = `
            <div class="cv-section" style="margin-bottom: 2rem;">
                <h3 style="color: ${template.colors.primary}; border-bottom: 2px solid ${template.colors.primary}; padding-bottom: 0.5rem; margin-bottom: 1rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                    ${isRTL ? 'الخبرة العملية' : 'Work Experience'}
                </h3>
        `;

        experiences.forEach(exp => {
            experienceHtml += `
                <div style="margin-bottom: 1.5rem; padding-${isRTL ? 'right' : 'left'}: 1rem; border-${isRTL ? 'right' : 'left'}: 3px solid ${template.colors.accent};">
                    <h4 style="color: ${template.colors.primary}; margin-bottom: 0.25rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                        ${exp.title} - ${exp.company}
                    </h4>
                    <p style="color: #6b7280; font-size: 0.9rem; margin-bottom: 0.5rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                        ${exp.startDate} - ${exp.endDate || (isRTL ? 'حتى الآن' : 'Present')} ${exp.location ? ` | ${exp.location}` : ''}
                    </p>
                    ${exp.description ? `<p style="line-height: 1.5; color: #374151; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">${exp.description}</p>` : ''}
                </div>
            `;
        });

        experienceHtml += '</div>';
        return experienceHtml;
    }

    generateEducationSection(education, template, isRTL) {
        let educationHtml = `
            <div class="cv-section" style="margin-bottom: 2rem;">
                <h3 style="color: ${template.colors.primary}; border-bottom: 2px solid ${template.colors.primary}; padding-bottom: 0.5rem; margin-bottom: 1rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                    ${isRTL ? 'التعليم' : 'Education'}
                </h3>
        `;

        education.forEach(edu => {
            educationHtml += `
                <div style="margin-bottom: 1rem; padding-${isRTL ? 'right' : 'left'}: 1rem; border-${isRTL ? 'right' : 'left'}: 3px solid ${template.colors.secondary};">
                    <h4 style="color: ${template.colors.primary}; margin-bottom: 0.25rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                        ${edu.degree} - ${edu.school}
                    </h4>
                    <p style="color: #6b7280; font-size: 0.9rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                        ${edu.startDate} - ${edu.endDate}
                    </p>
                </div>
            `;
        });

        educationHtml += '</div>';
        return educationHtml;
    }

    generateSkillsSection(skills, template, isRTL) {
        return `
            <div class="cv-section" style="margin-bottom: 2rem;">
                <h3 style="color: ${template.colors.primary}; border-bottom: 2px solid ${template.colors.primary}; padding-bottom: 0.5rem; margin-bottom: 1rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                    ${isRTL ? 'المهارات' : 'Skills'}
                </h3>
                <div style="display: flex; flex-wrap: wrap; gap: 0.5rem; ${isRTL ? 'justify-content: flex-end;' : ''}">
                    ${skills.map(skill => `
                        <span style="background: ${template.colors.primary}20; color: ${template.colors.primary}; padding: 0.25rem 0.75rem; border-radius: 1rem; font-size: 0.9rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'};">
                            ${skill.name} (${skill.level})
                        </span>
                    `).join('')}
                </div>
            </div>
        `;
    }

    generateProjectsSection(projects, template, isRTL) {
        let projectsHtml = `
            <div class="cv-section" style="margin-bottom: 2rem;">
                <h3 style="color: ${template.colors.primary}; border-bottom: 2px solid ${template.colors.primary}; padding-bottom: 0.5rem; margin-bottom: 1rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                    ${isRTL ? 'المشاريع' : 'Projects'}
                </h3>
        `;

        projects.forEach(project => {
            projectsHtml += `
                <div style="margin-bottom: 1rem; padding: 1rem; background: ${template.colors.primary}05; border-radius: 0.5rem;">
                    <h4 style="color: ${template.colors.primary}; margin-bottom: 0.5rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                        ${project.name}
                    </h4>
                    ${project.description ? `<p style="line-height: 1.5; color: #374151; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">${project.description}</p>` : ''}
                </div>
            `;
        });

        projectsHtml += '</div>';
        return projectsHtml;
    }

    generateLanguagesSection(languages, template, isRTL) {
        return `
            <div class="cv-section" style="margin-bottom: 2rem;">
                <h3 style="color: ${template.colors.primary}; border-bottom: 2px solid ${template.colors.primary}; padding-bottom: 0.5rem; margin-bottom: 1rem; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'}; text-align: ${isRTL ? 'right' : 'left'};">
                    ${isRTL ? 'اللغات' : 'Languages'}
                </h3>
                <div style="display: flex; flex-wrap: wrap; gap: 1rem; ${isRTL ? 'justify-content: flex-end;' : ''}">
                    ${languages.map(lang => `
                        <div style="text-align: ${isRTL ? 'right' : 'left'}; font-family: ${isRTL ? 'Cairo, Arial' : 'Inter, Arial'};">
                            <strong style="color: ${template.colors.primary};">${lang.name}</strong>
                            <br>
                            <span style="color: #6b7280; font-size: 0.9rem;">${lang.proficiency}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    getTemplatesList() {
        return Object.entries(this.templates).map(([id, template]) => ({
            id,
            name: this.isRTL ? template.name : template.nameEn,
            description: this.isRTL ? template.description : template.descriptionEn,
            colors: template.colors
        }));
    }
}

// Create global instance
window.arabicTemplateManager = new ArabicTemplateManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ArabicTemplateManager;
}
