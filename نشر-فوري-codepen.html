<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نشر فوري - CodePen</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        .btn.codepen {
            background: #000;
        }
        .btn.codepen:hover {
            background: #333;
        }
        .step {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        pre {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            direction: ltr;
            text-align: left;
        }
        .success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.5);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>🚀 نشر فوري عبر CodePen</h1>
        <p>أسرع طريقة لنشر التطبيق على الإنترنت خلال دقيقتين</p>

        <div class="success">
            ✅ <strong>التطبيق جاهز للنشر!</strong><br>
            قاعدة البيانات مكتملة ومفعلة مع جميع المميزات
        </div>

        <div class="step">
            <h3>الخطوة 1: افتح CodePen</h3>
            <a href="https://codepen.io/pen/" target="_blank" class="btn codepen">
                🖊️ افتح CodePen
            </a>
            <p>أو اذهب إلى: https://codepen.io/pen/</p>
        </div>

        <div class="step">
            <h3>الخطوة 2: انسخ الكود</h3>
            <p>انسخ الكود التالي والصقه في قسم HTML في CodePen:</p>
            <button onclick="copyCode()" class="btn">📋 نسخ الكود</button>
            
            <pre id="code-content" style="max-height: 300px; overflow-y: auto;">
&lt;!DOCTYPE html&gt;
&lt;html lang="ar" dir="rtl"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Elashrafy CV - منشئ السيرة الذاتية&lt;/title&gt;
    &lt;link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet"&gt;
    &lt;link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"&gt;
    &lt;script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"&gt;&lt;/script&gt;
    &lt;style&gt;
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Cairo', sans-serif; background: #f8fafc; color: #1e293b; direction: rtl; }
        .container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        header { background: white; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); position: sticky; top: 0; z-index: 1000; }
        nav { display: flex; justify-content: space-between; align-items: center; padding: 1rem 0; }
        .logo { font-size: 1.5rem; font-weight: 700; color: #2563eb; }
        .btn { padding: 0.5rem 1.5rem; border: none; border-radius: 0.5rem; font-weight: 500; cursor: pointer; transition: all 0.3s ease; text-decoration: none; display: inline-flex; align-items: center; gap: 0.5rem; }
        .btn-primary { background: #2563eb; color: white; }
        .btn-primary:hover { background: #1d4ed8; transform: translateY(-1px); }
        .btn-outline { background: transparent; color: #2563eb; border: 2px solid #2563eb; }
        .btn-outline:hover { background: #2563eb; color: white; }
        .hero { background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%); color: white; padding: 4rem 0; text-align: center; }
        .hero h1 { font-size: 3rem; margin-bottom: 1rem; font-weight: 700; }
        .hero p { font-size: 1.25rem; margin-bottom: 2rem; opacity: 0.9; }
        .status { background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); margin: 2rem 0; text-align: center; }
        .status.success { border-right: 4px solid #10b981; }
        .status.error { border-right: 4px solid #ef4444; }
        .status.info { border-right: 4px solid #2563eb; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.5); z-index: 2000; }
        .modal-content { background: white; margin: 5% auto; padding: 2rem; border-radius: 1rem; max-width: 500px; position: relative; }
        .close { position: absolute; top: 1rem; left: 1rem; font-size: 1.5rem; cursor: pointer; color: #64748b; }
        .form-group { margin-bottom: 1rem; }
        .form-group label { display: block; margin-bottom: 0.5rem; font-weight: 500; }
        .form-group input { width: 100%; padding: 0.75rem; border: 2px solid #e2e8f0; border-radius: 0.5rem; font-size: 1rem; }
        .form-group input:focus { outline: none; border-color: #2563eb; }
        .user-menu { display: none; align-items: center; gap: 1rem; }
        .user-avatar { width: 40px; height: 40px; border-radius: 50%; object-fit: cover; }
        .message { position: fixed; top: 20px; right: 20px; padding: 1rem 1.5rem; border-radius: 0.5rem; color: white; font-weight: 500; z-index: 3000; }
        .message.success { background: #10b981; }
        .message.error { background: #ef4444; }
        @media (max-width: 768px) { .hero h1 { font-size: 2rem; } }
    &lt;/style&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;header&gt;
        &lt;nav class="container"&gt;
            &lt;div class="logo"&gt;&lt;i class="fas fa-file-alt"&gt;&lt;/i&gt; Elashrafy CV&lt;/div&gt;
            &lt;div class="auth-buttons"&gt;
                &lt;button id="login-btn" class="btn btn-outline"&gt;&lt;i class="fas fa-sign-in-alt"&gt;&lt;/i&gt; تسجيل الدخول&lt;/button&gt;
                &lt;button id="signup-btn" class="btn btn-primary"&gt;&lt;i class="fas fa-user-plus"&gt;&lt;/i&gt; إنشاء حساب&lt;/button&gt;
                &lt;div id="user-menu" class="user-menu"&gt;
                    &lt;img id="user-avatar" class="user-avatar" src="" alt="صورة المستخدم"&gt;
                    &lt;span id="user-name"&gt;&lt;/span&gt;
                    &lt;button id="logout-btn" class="btn btn-outline"&gt;&lt;i class="fas fa-sign-out-alt"&gt;&lt;/i&gt; خروج&lt;/button&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/nav&gt;
    &lt;/header&gt;

    &lt;section class="hero"&gt;
        &lt;div class="container"&gt;
            &lt;h1&gt;أنشئ سيرتك الذاتية المهنية&lt;/h1&gt;
            &lt;p&gt;منصة شاملة لإنشاء السيرة الذاتية مع دعم رموز QR وبطاقات NFC&lt;/p&gt;
            &lt;button id="get-started-btn" class="btn btn-primary"&gt;&lt;i class="fas fa-rocket"&gt;&lt;/i&gt; ابدأ الآن مجاناً&lt;/button&gt;
        &lt;/div&gt;
    &lt;/section&gt;

    &lt;section class="container"&gt;
        &lt;div id="connection-status" class="status info"&gt;
            &lt;i class="fas fa-spinner fa-spin"&gt;&lt;/i&gt; جاري فحص الاتصال بقاعدة البيانات...
        &lt;/div&gt;
    &lt;/section&gt;

    &lt;div id="auth-modal" class="modal"&gt;
        &lt;div class="modal-content"&gt;
            &lt;span class="close"&gt;&times;&lt;/span&gt;
            &lt;div id="login-form"&gt;
                &lt;h2&gt;تسجيل الدخول&lt;/h2&gt;
                &lt;form&gt;
                    &lt;div class="form-group"&gt;
                        &lt;label for="login-email"&gt;البريد الإلكتروني&lt;/label&gt;
                        &lt;input type="email" id="login-email" required&gt;
                    &lt;/div&gt;
                    &lt;div class="form-group"&gt;
                        &lt;label for="login-password"&gt;كلمة المرور&lt;/label&gt;
                        &lt;input type="password" id="login-password" required&gt;
                    &lt;/div&gt;
                    &lt;button type="submit" class="btn btn-primary" style="width: 100%;"&gt;تسجيل الدخول&lt;/button&gt;
                &lt;/form&gt;
                &lt;p style="text-align: center; margin-top: 1rem;"&gt;
                    ليس لديك حساب؟ &lt;a href="#" id="show-signup" style="color: #2563eb;"&gt;إنشاء حساب&lt;/a&gt;
                &lt;/p&gt;
            &lt;/div&gt;
            &lt;div id="signup-form" style="display: none;"&gt;
                &lt;h2&gt;إنشاء حساب جديد&lt;/h2&gt;
                &lt;form&gt;
                    &lt;div class="form-group"&gt;
                        &lt;label for="signup-name"&gt;الاسم الكامل&lt;/label&gt;
                        &lt;input type="text" id="signup-name" required&gt;
                    &lt;/div&gt;
                    &lt;div class="form-group"&gt;
                        &lt;label for="signup-email"&gt;البريد الإلكتروني&lt;/label&gt;
                        &lt;input type="email" id="signup-email" required&gt;
                    &lt;/div&gt;
                    &lt;div class="form-group"&gt;
                        &lt;label for="signup-password"&gt;كلمة المرور&lt;/label&gt;
                        &lt;input type="password" id="signup-password" required&gt;
                    &lt;/div&gt;
                    &lt;button type="submit" class="btn btn-primary" style="width: 100%;"&gt;إنشاء حساب&lt;/button&gt;
                &lt;/form&gt;
                &lt;p style="text-align: center; margin-top: 1rem;"&gt;
                    لديك حساب بالفعل؟ &lt;a href="#" id="show-login" style="color: #2563eb;"&gt;تسجيل الدخول&lt;/a&gt;
                &lt;/p&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    &lt;/div&gt;

    &lt;script&gt;
        const SUPABASE_URL = 'https://hoyzvlfeyzzqbmhmsypy.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhveXp2bGZleXp6cWJtaG1zeXB5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MDk4NDQsImV4cCI6MjA2NTA4NTg0NH0.0PTd0lfyPxXQmU1akMISJYU3eAcLPCZzrJxXrKnus0U';
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
        let currentUser = null;

        async function checkConnection() {
            const statusEl = document.getElementById('connection-status');
            try {
                const { data, error } = await supabase.from('profiles').select('id').limit(1);
                if (error) throw error;
                statusEl.innerHTML = '&lt;i class="fas fa-check-circle"&gt;&lt;/i&gt; ✅ متصل بقاعدة البيانات - جاهز للاستخدام';
                statusEl.className = 'status success';
            } catch (error) {
                statusEl.innerHTML = '&lt;i class="fas fa-exclamation-triangle"&gt;&lt;/i&gt; ❌ خطأ في الاتصال بقاعدة البيانات';
                statusEl.className = 'status error';
            }
        }

        function showMessage(message, type = 'info') {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.textContent = message;
            document.body.appendChild(messageEl);
            setTimeout(() =&gt; messageEl.remove(), 5000);
        }

        function openAuthModal() { document.getElementById('auth-modal').style.display = 'block'; }
        function closeAuthModal() { document.getElementById('auth-modal').style.display = 'none'; }
        function showLoginForm() { document.getElementById('login-form').style.display = 'block'; document.getElementById('signup-form').style.display = 'none'; }
        function showSignupForm() { document.getElementById('login-form').style.display = 'none'; document.getElementById('signup-form').style.display = 'block'; }

        function updateUI(isAuthenticated) {
            const authButtons = document.querySelector('.auth-buttons');
            const userMenu = document.getElementById('user-menu');
            if (isAuthenticated && currentUser) {
                authButtons.style.display = 'none';
                userMenu.style.display = 'flex';
                document.getElementById('user-name').textContent = currentUser.email;
                document.getElementById('user-avatar').src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentUser.email)}&background=2563eb&color=fff`;
            } else {
                authButtons.style.display = 'flex';
                userMenu.style.display = 'none';
            }
        }

        async function signIn(email, password) {
            try {
                const { data, error } = await supabase.auth.signInWithPassword({ email, password });
                if (error) throw error;
                currentUser = data.user;
                updateUI(true);
                closeAuthModal();
                showMessage('تم تسجيل الدخول بنجاح!', 'success');
            } catch (error) {
                showMessage('خطأ في تسجيل الدخول: ' + error.message, 'error');
            }
        }

        async function signUp(email, password, fullName) {
            try {
                const { data, error } = await supabase.auth.signUp({
                    email, password,
                    options: { data: { full_name: fullName } }
                });
                if (error) throw error;
                showMessage('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول.', 'success');
                showLoginForm();
                document.getElementById('login-email').value = email;
            } catch (error) {
                showMessage('خطأ في إنشاء الحساب: ' + error.message, 'error');
            }
        }

        async function signOut() {
            try {
                const { error } = await supabase.auth.signOut();
                if (error) throw error;
                currentUser = null;
                updateUI(false);
                showMessage('تم تسجيل الخروج بنجاح', 'success');
            } catch (error) {
                showMessage('خطأ في تسجيل الخروج: ' + error.message, 'error');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
            document.getElementById('login-btn').addEventListener('click', () =&gt; { showLoginForm(); openAuthModal(); });
            document.getElementById('signup-btn').addEventListener('click', () =&gt; { showSignupForm(); openAuthModal(); });
            document.getElementById('get-started-btn').addEventListener('click', () =&gt; {
                if (currentUser) showMessage('مرحباً بك في لوحة التحكم!', 'success');
                else { showSignupForm(); openAuthModal(); }
            });
            document.getElementById('logout-btn').addEventListener('click', signOut);
            document.querySelector('.close').addEventListener('click', closeAuthModal);
            document.getElementById('show-signup').addEventListener('click', (e) =&gt; { e.preventDefault(); showSignupForm(); });
            document.getElementById('show-login').addEventListener('click', (e) =&gt; { e.preventDefault(); showLoginForm(); });
            document.querySelector('#login-form form').addEventListener('submit', (e) =&gt; {
                e.preventDefault();
                signIn(document.getElementById('login-email').value, document.getElementById('login-password').value);
            });
            document.querySelector('#signup-form form').addEventListener('submit', (e) =&gt; {
                e.preventDefault();
                signUp(document.getElementById('signup-email').value, document.getElementById('signup-password').value, document.getElementById('signup-name').value);
            });
            window.addEventListener('click', (e) =&gt; { if (e.target === document.getElementById('auth-modal')) closeAuthModal(); });
            supabase.auth.getSession().then(({ data: { session } }) =&gt; {
                if (session) { currentUser = session.user; updateUI(true); }
            });
            supabase.auth.onAuthStateChange((event, session) =&gt; {
                if (event === 'SIGNED_IN') { currentUser = session.user; updateUI(true); }
                else if (event === 'SIGNED_OUT') { currentUser = null; updateUI(false); }
            });
        });
    &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;
            </pre>
        </div>

        <div class="step">
            <h3>الخطوة 3: احفظ واختبر</h3>
            <p>1. اضغط "Save" في CodePen</p>
            <p>2. اختبر التطبيق مباشرة</p>
            <p>3. احصل على رابط مشاركة من "Share" > "Live View"</p>
        </div>

        <div class="success">
            🎉 <strong>تهانينا!</strong><br>
            التطبيق الآن منشور ويعمل على الإنترنت مع قاعدة البيانات الحقيقية
        </div>

        <div class="step">
            <h3>🧪 اختبار المميزات</h3>
            <ul>
                <li>✅ إنشاء حساب جديد</li>
                <li>✅ تسجيل الدخول</li>
                <li>✅ فحص الاتصال بقاعدة البيانات</li>
                <li>✅ واجهة مستخدم متجاوبة</li>
            </ul>
        </div>
    </div>

    <script>
        function copyCode() {
            const codeContent = document.getElementById('code-content');
            const textArea = document.createElement('textarea');
            textArea.value = codeContent.textContent;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            
            alert('تم نسخ الكود! الصقه في CodePen');
        }
    </script>
</body>
</html>
