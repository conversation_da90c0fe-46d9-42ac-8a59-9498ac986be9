// Translation System for Arabic/English Support
class TranslationManager {
    constructor() {
        this.currentLanguage = 'ar'; // Default to Arabic
        this.translations = {
            ar: {
                // Navigation
                'nav.home': 'الرئيسية',
                'nav.features': 'المميزات',
                'nav.templates': 'القوالب',
                'nav.pricing': 'الأسعار',
                'nav.dashboard': 'لوحة التحكم',
                'nav.login': 'تسجيل الدخول',
                'nav.signup': 'إنشاء حساب',
                'nav.logout': 'تسجيل الخروج',
                'nav.profile': 'الملف الشخصي',

                // Hero Section
                'hero.title': 'أنشئ سيرتك الذاتية المهنية في دقائق',
                'hero.subtitle': 'اصنع سيرة ذاتية مذهلة، وولد رموز QR، وأدر بطاقات NFC باستخدام منصتنا المدعومة بالذكاء الاصطناعي',
                'hero.get_started': 'ابدأ مجاناً',
                'hero.watch_demo': 'شاهد العرض التوضيحي',

                // Features Section
                'features.title': 'مميزات قوية',
                'features.subtitle': 'كل ما تحتاجه لإنشاء ومشاركة ملفك المهني',
                'features.cv_builder.title': 'منشئ السيرة الذاتية',
                'features.cv_builder.description': 'أنشئ سيرة ذاتية مهنية باستخدام منشئنا البديهي واقتراحات الذكاء الاصطناعي.',
                'features.cv_builder.action': 'جرب الآن',
                'features.qr_generator.title': 'مولد رمز QR',
                'features.qr_generator.description': 'ولد رموز QR لسيرتك الذاتية ومعلومات الاتصال للمشاركة السهلة.',
                'features.qr_generator.action': 'ولد',
                'features.nfc_cards.title': 'بطاقات NFC',
                'features.nfc_cards.description': 'أنشئ وأدر بطاقات العمل الرقمية للأجهزة المدعومة بـ NFC.',
                'features.nfc_cards.action': 'إدارة',
                'features.ai_assistant.title': 'مساعد الذكاء الاصطناعي',
                'features.ai_assistant.description': 'احصل على اقتراحات المحتوى ونصائح التحسين المدعومة بالذكاء الاصطناعي لسيرتك الذاتية.',
                'features.ai_assistant.action': 'اعرف المزيد',
                'features.templates.title': 'قوالب مهنية',
                'features.templates.description': 'اختر من عشرات القوالب المصممة بشكل مهني.',
                'features.templates.action': 'تصفح',
                'features.export.title': 'تصدير ومشاركة',
                'features.export.description': 'صدر إلى PDF، شارك عبر الإنترنت، أو اطبع سيرتك الذاتية بنقرة واحدة.',
                'features.export.action': 'تصدير',

                // Templates Section
                'templates.title': 'قوالب مهنية',
                'templates.subtitle': 'اختر من مجموعتنا من القوالب الحديثة المتوافقة مع أنظمة التتبع',
                'templates.modern.name': 'مهني حديث',
                'templates.modern.description': 'تصميم نظيف وحديث مثالي للمهنيين التقنيين',
                'templates.classic.name': 'تنفيذي كلاسيكي',
                'templates.classic.description': 'تخطيط تقليدي مثالي للمناصب التنفيذية',
                'templates.creative.name': 'مصمم إبداعي',
                'templates.creative.description': 'تصميم جريء وإبداعي للمهنيين المبدعين',
                'templates.use_template': 'استخدم القالب',
                'templates.view_all': 'عرض جميع القوالب',

                // Pricing Section
                'pricing.title': 'أسعار بسيطة',
                'pricing.subtitle': 'اختر الخطة التي تناسبك',
                'pricing.free.title': 'مجاني',
                'pricing.free.price': '0 ريال',
                'pricing.free.period': '/شهر',
                'pricing.pro.title': 'احترافي',
                'pricing.pro.price': '37 ريال',
                'pricing.pro.period': '/شهر',
                'pricing.pro.badge': 'الأكثر شعبية',
                'pricing.enterprise.title': 'مؤسسي',
                'pricing.enterprise.price': 'مخصص',
                'pricing.get_started': 'ابدأ',
                'pricing.upgrade': 'ترقية الآن',
                'pricing.contact_sales': 'اتصل بالمبيعات',

                // Features List
                'pricing.features.templates_3': '3 قوالب سيرة ذاتية',
                'pricing.features.templates_all': 'جميع القوالب',
                'pricing.features.qr_basic': 'رموز QR أساسية',
                'pricing.features.qr_advanced': 'رموز QR متقدمة',
                'pricing.features.pdf_export': 'تصدير PDF',
                'pricing.features.ai_suggestions': 'اقتراحات الذكاء الاصطناعي',
                'pricing.features.nfc_cards': 'بطاقات NFC',
                'pricing.features.priority_support': 'دعم أولوية',
                'pricing.features.custom_templates': 'قوالب مخصصة',
                'pricing.features.team_management': 'إدارة الفريق',
                'pricing.features.api_access': 'وصول API',
                'pricing.features.dedicated_support': 'دعم مخصص',

                // Footer
                'footer.description': 'المنصة الشاملة لإنشاء السير الذاتية المهنية ورموز QR وبطاقات NFC.',
                'footer.product': 'المنتج',
                'footer.support': 'الدعم',
                'footer.contact': 'اتصل بنا',
                'footer.help_center': 'مركز المساعدة',
                'footer.contact_us': 'اتصل بنا',
                'footer.privacy': 'سياسة الخصوصية',
                'footer.terms': 'شروط الخدمة',
                'footer.rights': 'جميع الحقوق محفوظة',
                'footer.designed_by': 'تصميم محمد الأشرافي',

                // Authentication
                'auth.login.title': 'تسجيل الدخول إلى حسابك',
                'auth.signup.title': 'إنشاء حسابك',
                'auth.email': 'البريد الإلكتروني',
                'auth.password': 'كلمة المرور',
                'auth.full_name': 'الاسم الكامل',
                'auth.login_button': 'تسجيل الدخول',
                'auth.signup_button': 'إنشاء حساب',
                'auth.no_account': 'ليس لديك حساب؟',
                'auth.have_account': 'لديك حساب بالفعل؟',
                'auth.signup_link': 'إنشاء حساب',
                'auth.login_link': 'تسجيل الدخول',

                // CV Builder
                'cv.title': 'منشئ السيرة الذاتية',
                'cv.save': 'حفظ',
                'cv.export_pdf': 'تصدير PDF',
                'cv.cv_title': 'عنوان السيرة الذاتية',
                'cv.template': 'القالب',
                'cv.sections': 'أقسام السيرة الذاتية',
                'cv.personal_info': 'المعلومات الشخصية',
                'cv.professional_summary': 'الملخص المهني',
                'cv.work_experience': 'الخبرة العملية',
                'cv.education': 'التعليم',
                'cv.skills': 'المهارات',
                'cv.projects': 'المشاريع',
                'cv.certifications': 'الشهادات',
                'cv.languages': 'اللغات',

                // Form Fields
                'form.full_name': 'الاسم الكامل',
                'form.job_title': 'المسمى الوظيفي',
                'form.email': 'البريد الإلكتروني',
                'form.phone': 'رقم الهاتف',
                'form.location': 'الموقع',
                'form.website': 'الموقع الإلكتروني',
                'form.linkedin': 'لينكد إن',
                'form.github': 'جيت هاب',
                'form.profile_photo': 'الصورة الشخصية',
                'form.upload_photo': 'انقر لرفع الصورة',
                'form.summary': 'الملخص',
                'form.required': 'مطلوب',

                // Messages
                'message.login_required': 'يرجى تسجيل الدخول للوصول إلى منشئ السيرة الذاتية',
                'message.save_success': 'تم حفظ السيرة الذاتية بنجاح',
                'message.save_error': 'حدث خطأ أثناء حفظ السيرة الذاتية',
                'message.skill_exists': 'المهارة موجودة بالفعل',

                // Common
                'common.add': 'إضافة',
                'common.edit': 'تعديل',
                'common.delete': 'حذف',
                'common.cancel': 'إلغاء',
                'common.save': 'حفظ',
                'common.close': 'إغلاق',
                'common.loading': 'جاري التحميل...',
                'common.error': 'خطأ',
                'common.success': 'نجح',
                'common.warning': 'تحذير',
                'common.info': 'معلومات'
            },
            en: {
                // Navigation
                'nav.home': 'Home',
                'nav.features': 'Features',
                'nav.templates': 'Templates',
                'nav.pricing': 'Pricing',
                'nav.dashboard': 'Dashboard',
                'nav.login': 'Login',
                'nav.signup': 'Sign Up',
                'nav.logout': 'Logout',
                'nav.profile': 'Profile',

                // Hero Section
                'hero.title': 'Create Your Professional CV in Minutes',
                'hero.subtitle': 'Build stunning CVs, generate QR codes, and manage NFC cards with our AI-powered platform',
                'hero.get_started': 'Get Started Free',
                'hero.watch_demo': 'Watch Demo',

                // Features Section
                'features.title': 'Powerful Features',
                'features.subtitle': 'Everything you need to create and share your professional profile',
                'features.cv_builder.title': 'CV Builder',
                'features.cv_builder.description': 'Create professional CVs with our intuitive drag-and-drop builder and AI-powered suggestions.',
                'features.cv_builder.action': 'Try Now',
                'features.qr_generator.title': 'QR Code Generator',
                'features.qr_generator.description': 'Generate QR codes for your CV and contact information for easy sharing.',
                'features.qr_generator.action': 'Generate',
                'features.nfc_cards.title': 'NFC Cards',
                'features.nfc_cards.description': 'Create and manage digital business cards for NFC-enabled devices.',
                'features.nfc_cards.action': 'Manage',
                'features.ai_assistant.title': 'AI Assistant',
                'features.ai_assistant.description': 'Get AI-powered content suggestions and optimization tips for your CV.',
                'features.ai_assistant.action': 'Learn More',
                'features.templates.title': 'Professional Templates',
                'features.templates.description': 'Choose from dozens of professionally designed templates.',
                'features.templates.action': 'Browse',
                'features.export.title': 'Export & Share',
                'features.export.description': 'Export to PDF, share online, or print your CV with one click.',
                'features.export.action': 'Export'
                // ... (rest of English translations would continue here)
            }
        };
        
        this.init();
    }

    init() {
        this.detectLanguage();
        this.createLanguageSwitcher();
        this.translatePage();
    }

    detectLanguage() {
        // Check localStorage first
        const savedLang = localStorage.getItem('preferred_language');
        if (savedLang && this.translations[savedLang]) {
            this.currentLanguage = savedLang;
            return;
        }

        // Check browser language
        const browserLang = navigator.language || navigator.userLanguage;
        if (browserLang.startsWith('ar')) {
            this.currentLanguage = 'ar';
        } else {
            this.currentLanguage = 'en';
        }
    }

    createLanguageSwitcher() {
        const switcher = document.createElement('div');
        switcher.className = 'language-switcher';
        switcher.innerHTML = `
            <button class="lang-btn ${this.currentLanguage === 'ar' ? 'active' : ''}" data-lang="ar">العربية</button>
            <button class="lang-btn ${this.currentLanguage === 'en' ? 'active' : ''}" data-lang="en">English</button>
        `;

        document.body.appendChild(switcher);

        // Add event listeners
        switcher.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.switchLanguage(btn.dataset.lang);
            });
        });
    }

    switchLanguage(lang) {
        if (!this.translations[lang]) return;

        this.currentLanguage = lang;
        localStorage.setItem('preferred_language', lang);

        // Update HTML attributes
        document.documentElement.lang = lang;
        document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';

        // Update body class
        document.body.className = lang === 'ar' ? 'rtl-layout' : '';

        // Update language switcher
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.lang === lang);
        });

        // Translate page
        this.translatePage();

        // Trigger custom event for other components
        window.dispatchEvent(new CustomEvent('languageChanged', { detail: { language: lang } }));
    }

    translatePage() {
        // Translate elements with data-translate attribute
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.get(key);
            if (translation) {
                if (element.tagName === 'INPUT' && element.type === 'text') {
                    element.placeholder = translation;
                } else {
                    element.textContent = translation;
                }
            }
        });

        // Update page title
        if (this.currentLanguage === 'ar') {
            document.title = 'الأشرافي CV - منشئ السيرة الذاتية المهنية';
        } else {
            document.title = 'Elashrafy CV - Professional CV Builder';
        }
    }

    get(key) {
        return this.translations[this.currentLanguage][key] || key;
    }

    getCurrentLanguage() {
        return this.currentLanguage;
    }

    isRTL() {
        return this.currentLanguage === 'ar';
    }
}

// Initialize translation manager
window.translationManager = new TranslationManager();

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TranslationManager;
}
