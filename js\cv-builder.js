// CV Builder Management
class CVBuilder {
    constructor() {
        this.currentUser = null;
        this.currentCV = null;
        this.cvData = this.getDefaultCVData();
        this.undoStack = [];
        this.redoStack = [];
        this.maxUndoSteps = 50;
        this.autoSaveInterval = null;
        this.init();
    }

    async init() {
        // Check authentication
        if (!window.authManager) {
            console.error('Auth manager not available');
            return;
        }

        await this.waitForAuth();

        if (!window.authManager.isAuthenticated()) {
            window.authManager.showErrorMessage('Please login to access the CV builder');
            setTimeout(() => {
                window.location.href = '../index.html';
            }, 2000);
            return;
        }

        this.currentUser = window.authManager.getCurrentUser();
        
        // Check if editing existing CV
        const urlParams = new URLSearchParams(window.location.search);
        const cvId = urlParams.get('id');
        const templateId = urlParams.get('template');

        if (cvId) {
            await this.loadCV(cvId);
        } else if (templateId) {
            await this.loadTemplate(templateId);
        }

        this.setupEventListeners();
        this.setupAutoSave();
        this.updatePreview();
    }

    async waitForAuth() {
        return new Promise((resolve) => {
            const checkAuth = () => {
                if (window.authManager && window.authManager.currentUser !== undefined) {
                    resolve();
                } else {
                    setTimeout(checkAuth, 100);
                }
            };
            checkAuth();
        });
    }

    getDefaultCVData() {
        return {
            title: 'My Professional CV',
            template: 'modern',
            personal: {
                fullName: '',
                jobTitle: '',
                email: '',
                phone: '',
                location: '',
                website: '',
                linkedin: '',
                github: '',
                photo: null
            },
            summary: '',
            experience: [],
            education: [],
            skills: [],
            projects: [],
            certifications: [],
            languages: []
        };
    }

    setupEventListeners() {
        // Section navigation
        document.querySelectorAll('.section-item').forEach(item => {
            item.addEventListener('click', () => {
                this.switchSection(item.dataset.section);
            });
        });

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.switchTab(btn.dataset.tab);
            });
        });

        // Form inputs
        this.setupFormListeners();

        // Action buttons
        this.setupActionButtons();

        // Sidebar toggle
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                document.querySelector('.cv-sidebar').classList.toggle('collapsed');
            });
        }

        // Photo upload
        this.setupPhotoUpload();

        // Skills management
        this.setupSkillsManagement();

        // Dynamic sections
        this.setupDynamicSections();
    }

    setupFormListeners() {
        // Personal information
        const personalFields = ['cv-title', 'cv-template', 'full-name', 'job-title', 'email', 'phone', 'location', 'website', 'linkedin', 'github'];
        personalFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', () => {
                    this.updateCVData();
                    this.updatePreview();
                });
            }
        });

        // Professional summary
        const summaryField = document.getElementById('professional-summary');
        if (summaryField) {
            summaryField.addEventListener('input', () => {
                this.updateCVData();
                this.updatePreview();
            });
        }
    }

    setupActionButtons() {
        // Save CV
        const saveCVBtn = document.getElementById('save-cv-btn');
        if (saveCVBtn) {
            saveCVBtn.addEventListener('click', () => {
                this.saveCV();
            });
        }

        // Export PDF
        const exportPDFBtn = document.getElementById('export-pdf-btn');
        if (exportPDFBtn) {
            exportPDFBtn.addEventListener('click', () => {
                this.exportToPDF();
            });
        }

        // Undo/Redo
        const undoBtn = document.getElementById('undo-btn');
        const redoBtn = document.getElementById('redo-btn');
        
        if (undoBtn) {
            undoBtn.addEventListener('click', () => {
                this.undo();
            });
        }
        
        if (redoBtn) {
            redoBtn.addEventListener('click', () => {
                this.redo();
            });
        }

        // AI suggestions
        const getAISuggestionsBtn = document.getElementById('get-ai-suggestions');
        if (getAISuggestionsBtn) {
            getAISuggestionsBtn.addEventListener('click', () => {
                this.getAISuggestions();
            });
        }

        const generateSummaryBtn = document.getElementById('generate-summary');
        if (generateSummaryBtn) {
            generateSummaryBtn.addEventListener('click', () => {
                this.generateSummary();
            });
        }
    }

    setupPhotoUpload() {
        const photoUpload = document.getElementById('photo-upload');
        const photoPreview = document.getElementById('photo-preview');

        if (photoPreview) {
            photoPreview.addEventListener('click', () => {
                photoUpload.click();
            });
        }

        if (photoUpload) {
            photoUpload.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handlePhotoUpload(file);
                }
            });
        }
    }

    setupSkillsManagement() {
        const addSkillBtn = document.getElementById('add-skill-btn');
        const newSkillInput = document.getElementById('new-skill');
        const skillLevelSelect = document.getElementById('skill-level');

        if (addSkillBtn) {
            addSkillBtn.addEventListener('click', () => {
                const skill = newSkillInput.value.trim();
                const level = skillLevelSelect.value;
                
                if (skill) {
                    this.addSkill(skill, level);
                    newSkillInput.value = '';
                    skillLevelSelect.value = 'intermediate';
                }
            });
        }

        if (newSkillInput) {
            newSkillInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    addSkillBtn.click();
                }
            });
        }
    }

    setupDynamicSections() {
        // Add buttons for dynamic sections
        const addButtons = [
            { id: 'add-experience', section: 'experience' },
            { id: 'add-education', section: 'education' },
            { id: 'add-project', section: 'projects' },
            { id: 'add-certification', section: 'certifications' },
            { id: 'add-language', section: 'languages' }
        ];

        addButtons.forEach(({ id, section }) => {
            const btn = document.getElementById(id);
            if (btn) {
                btn.addEventListener('click', () => {
                    this.addDynamicItem(section);
                });
            }
        });
    }

    switchSection(sectionName) {
        // Update sidebar
        document.querySelectorAll('.section-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');

        // Update editor
        document.querySelectorAll('.editor-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`).classList.add('active');
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.editor-content').forEach(content => {
            content.style.display = 'none';
        });
        document.getElementById(`${tabName}-tab`).style.display = 'block';

        if (tabName === 'preview') {
            this.updatePreview();
        }
    }

    updateCVData() {
        // Save current state for undo
        this.saveState();

        // Update CV data from form
        this.cvData.title = document.getElementById('cv-title')?.value || this.cvData.title;
        this.cvData.template = document.getElementById('cv-template')?.value || this.cvData.template;

        // Personal information
        const personal = this.cvData.personal;
        personal.fullName = document.getElementById('full-name')?.value || '';
        personal.jobTitle = document.getElementById('job-title')?.value || '';
        personal.email = document.getElementById('email')?.value || '';
        personal.phone = document.getElementById('phone')?.value || '';
        personal.location = document.getElementById('location')?.value || '';
        personal.website = document.getElementById('website')?.value || '';
        personal.linkedin = document.getElementById('linkedin')?.value || '';
        personal.github = document.getElementById('github')?.value || '';

        // Professional summary
        this.cvData.summary = document.getElementById('professional-summary')?.value || '';
    }

    saveState() {
        this.undoStack.push(JSON.parse(JSON.stringify(this.cvData)));
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }
        this.redoStack = [];
        this.updateUndoRedoButtons();
    }

    undo() {
        if (this.undoStack.length > 0) {
            this.redoStack.push(JSON.parse(JSON.stringify(this.cvData)));
            this.cvData = this.undoStack.pop();
            this.populateForm();
            this.updatePreview();
            this.updateUndoRedoButtons();
        }
    }

    redo() {
        if (this.redoStack.length > 0) {
            this.undoStack.push(JSON.parse(JSON.stringify(this.cvData)));
            this.cvData = this.redoStack.pop();
            this.populateForm();
            this.updatePreview();
            this.updateUndoRedoButtons();
        }
    }

    updateUndoRedoButtons() {
        const undoBtn = document.getElementById('undo-btn');
        const redoBtn = document.getElementById('redo-btn');
        
        if (undoBtn) {
            undoBtn.disabled = this.undoStack.length === 0;
        }
        
        if (redoBtn) {
            redoBtn.disabled = this.redoStack.length === 0;
        }
    }

    populateForm() {
        // Populate form fields with CV data
        const fields = {
            'cv-title': this.cvData.title,
            'cv-template': this.cvData.template,
            'full-name': this.cvData.personal.fullName,
            'job-title': this.cvData.personal.jobTitle,
            'email': this.cvData.personal.email,
            'phone': this.cvData.personal.phone,
            'location': this.cvData.personal.location,
            'website': this.cvData.personal.website,
            'linkedin': this.cvData.personal.linkedin,
            'github': this.cvData.personal.github,
            'professional-summary': this.cvData.summary
        };

        Object.entries(fields).forEach(([id, value]) => {
            const field = document.getElementById(id);
            if (field) {
                field.value = value || '';
            }
        });

        // Populate dynamic sections
        this.populateSkills();
        this.populateExperience();
        this.populateEducation();
        this.populateProjects();
        this.populateCertifications();
        this.populateLanguages();
    }

    populateSkills() {
        const skillsList = document.getElementById('skills-list');
        if (!skillsList) return;

        skillsList.innerHTML = '';
        this.cvData.skills.forEach((skill, index) => {
            const skillTag = this.createSkillTag(skill, index);
            skillsList.appendChild(skillTag);
        });
    }

    createSkillTag(skill, index) {
        const tag = document.createElement('div');
        tag.className = 'skill-tag';
        tag.innerHTML = `
            <span>${skill.name}</span>
            <span class="skill-level">(${skill.level})</span>
            <button class="skill-remove" onclick="cvBuilder.removeSkill(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        return tag;
    }

    addSkill(name, level) {
        // Check if skill already exists
        const existingSkill = this.cvData.skills.find(skill => 
            skill.name.toLowerCase() === name.toLowerCase()
        );

        if (existingSkill) {
            this.showMessage('Skill already exists', 'warning');
            return;
        }

        this.saveState();
        this.cvData.skills.push({ name, level });
        this.populateSkills();
        this.updatePreview();
    }

    removeSkill(index) {
        this.saveState();
        this.cvData.skills.splice(index, 1);
        this.populateSkills();
        this.updatePreview();
    }

    addDynamicItem(section) {
        this.saveState();
        
        const newItem = this.getDefaultItemForSection(section);
        this.cvData[section].push(newItem);
        
        // Populate the section
        this[`populate${this.capitalize(section)}`]();
        this.updatePreview();
    }

    getDefaultItemForSection(section) {
        const defaults = {
            experience: {
                title: '',
                company: '',
                location: '',
                startDate: '',
                endDate: '',
                current: false,
                description: ''
            },
            education: {
                degree: '',
                school: '',
                location: '',
                startDate: '',
                endDate: '',
                gpa: '',
                description: ''
            },
            projects: {
                name: '',
                description: '',
                technologies: '',
                url: '',
                startDate: '',
                endDate: ''
            },
            certifications: {
                name: '',
                issuer: '',
                date: '',
                url: '',
                description: ''
            },
            languages: {
                name: '',
                proficiency: 'intermediate'
            }
        };

        return defaults[section] || {};
    }

    populateExperience() {
        const experienceList = document.getElementById('experience-list');
        if (!experienceList) return;

        experienceList.innerHTML = '';
        this.cvData.experience.forEach((exp, index) => {
            const expItem = this.createExperienceItem(exp, index);
            experienceList.appendChild(expItem);
        });
    }

    createExperienceItem(exp, index) {
        const item = document.createElement('div');
        item.className = 'experience-item';
        item.innerHTML = `
            <button class="remove-item" onclick="cvBuilder.removeItem('experience', ${index})">
                <i class="fas fa-times"></i>
            </button>
            <div class="form-grid">
                <div class="form-group">
                    <label>Job Title</label>
                    <input type="text" value="${exp.title}" onchange="cvBuilder.updateItem('experience', ${index}, 'title', this.value)">
                </div>
                <div class="form-group">
                    <label>Company</label>
                    <input type="text" value="${exp.company}" onchange="cvBuilder.updateItem('experience', ${index}, 'company', this.value)">
                </div>
                <div class="form-group">
                    <label>Location</label>
                    <input type="text" value="${exp.location}" onchange="cvBuilder.updateItem('experience', ${index}, 'location', this.value)">
                </div>
                <div class="form-group">
                    <label>Start Date</label>
                    <input type="month" value="${exp.startDate}" onchange="cvBuilder.updateItem('experience', ${index}, 'startDate', this.value)">
                </div>
                <div class="form-group">
                    <label>End Date</label>
                    <input type="month" value="${exp.endDate}" ${exp.current ? 'disabled' : ''} onchange="cvBuilder.updateItem('experience', ${index}, 'endDate', this.value)">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" ${exp.current ? 'checked' : ''} onchange="cvBuilder.updateItem('experience', ${index}, 'current', this.checked)">
                        Current Position
                    </label>
                </div>
            </div>
            <div class="form-group">
                <label>Description</label>
                <textarea rows="4" placeholder="Describe your responsibilities and achievements..." onchange="cvBuilder.updateItem('experience', ${index}, 'description', this.value)">${exp.description}</textarea>
            </div>
        `;
        return item;
    }

    updateItem(section, index, field, value) {
        this.saveState();
        this.cvData[section][index][field] = value;
        
        // Handle current position checkbox
        if (field === 'current' && value) {
            this.cvData[section][index].endDate = '';
        }
        
        this.updatePreview();
    }

    removeItem(section, index) {
        if (confirm('Are you sure you want to remove this item?')) {
            this.saveState();
            this.cvData[section].splice(index, 1);
            this[`populate${this.capitalize(section)}`]();
            this.updatePreview();
        }
    }

    // Education section methods
    populateEducation() {
        const educationList = document.getElementById('education-list');
        if (!educationList) return;

        educationList.innerHTML = '';
        this.cvData.education.forEach((edu, index) => {
            const eduItem = this.createEducationItem(edu, index);
            educationList.appendChild(eduItem);
        });
    }

    createEducationItem(edu, index) {
        const item = document.createElement('div');
        item.className = 'education-item';
        item.innerHTML = `
            <button class="remove-item" onclick="cvBuilder.removeItem('education', ${index})">
                <i class="fas fa-times"></i>
            </button>
            <div class="form-grid">
                <div class="form-group">
                    <label>الدرجة العلمية</label>
                    <input type="text" value="${edu.degree}" onchange="cvBuilder.updateItem('education', ${index}, 'degree', this.value)">
                </div>
                <div class="form-group">
                    <label>المؤسسة التعليمية</label>
                    <input type="text" value="${edu.school}" onchange="cvBuilder.updateItem('education', ${index}, 'school', this.value)">
                </div>
                <div class="form-group">
                    <label>الموقع</label>
                    <input type="text" value="${edu.location}" onchange="cvBuilder.updateItem('education', ${index}, 'location', this.value)">
                </div>
                <div class="form-group">
                    <label>تاريخ البداية</label>
                    <input type="month" value="${edu.startDate}" onchange="cvBuilder.updateItem('education', ${index}, 'startDate', this.value)">
                </div>
                <div class="form-group">
                    <label>تاريخ النهاية</label>
                    <input type="month" value="${edu.endDate}" onchange="cvBuilder.updateItem('education', ${index}, 'endDate', this.value)">
                </div>
                <div class="form-group">
                    <label>المعدل التراكمي</label>
                    <input type="text" value="${edu.gpa}" onchange="cvBuilder.updateItem('education', ${index}, 'gpa', this.value)">
                </div>
            </div>
            <div class="form-group">
                <label>الوصف</label>
                <textarea rows="3" placeholder="اذكر التخصص، الأنشطة، الإنجازات..." onchange="cvBuilder.updateItem('education', ${index}, 'description', this.value)">${edu.description}</textarea>
            </div>
        `;
        return item;
    }

    // Projects section methods
    populateProjects() {
        const projectsList = document.getElementById('projects-list');
        if (!projectsList) return;

        projectsList.innerHTML = '';
        this.cvData.projects.forEach((project, index) => {
            const projectItem = this.createProjectItem(project, index);
            projectsList.appendChild(projectItem);
        });
    }

    createProjectItem(project, index) {
        const item = document.createElement('div');
        item.className = 'project-item';
        item.innerHTML = `
            <button class="remove-item" onclick="cvBuilder.removeItem('projects', ${index})">
                <i class="fas fa-times"></i>
            </button>
            <div class="form-grid">
                <div class="form-group">
                    <label>اسم المشروع</label>
                    <input type="text" value="${project.name}" onchange="cvBuilder.updateItem('projects', ${index}, 'name', this.value)">
                </div>
                <div class="form-group">
                    <label>التقنيات المستخدمة</label>
                    <input type="text" value="${project.technologies}" onchange="cvBuilder.updateItem('projects', ${index}, 'technologies', this.value)">
                </div>
                <div class="form-group">
                    <label>رابط المشروع</label>
                    <input type="url" value="${project.url}" onchange="cvBuilder.updateItem('projects', ${index}, 'url', this.value)">
                </div>
                <div class="form-group">
                    <label>تاريخ البداية</label>
                    <input type="month" value="${project.startDate}" onchange="cvBuilder.updateItem('projects', ${index}, 'startDate', this.value)">
                </div>
                <div class="form-group">
                    <label>تاريخ النهاية</label>
                    <input type="month" value="${project.endDate}" onchange="cvBuilder.updateItem('projects', ${index}, 'endDate', this.value)">
                </div>
            </div>
            <div class="form-group">
                <label>وصف المشروع</label>
                <textarea rows="4" placeholder="اشرح المشروع، دورك فيه، والنتائج المحققة..." onchange="cvBuilder.updateItem('projects', ${index}, 'description', this.value)">${project.description}</textarea>
            </div>
        `;
        return item;
    }

    // Certifications section methods
    populateCertifications() {
        const certificationsList = document.getElementById('certifications-list');
        if (!certificationsList) return;

        certificationsList.innerHTML = '';
        this.cvData.certifications.forEach((cert, index) => {
            const certItem = this.createCertificationItem(cert, index);
            certificationsList.appendChild(certItem);
        });
    }

    createCertificationItem(cert, index) {
        const item = document.createElement('div');
        item.className = 'certification-item';
        item.innerHTML = `
            <button class="remove-item" onclick="cvBuilder.removeItem('certifications', ${index})">
                <i class="fas fa-times"></i>
            </button>
            <div class="form-grid">
                <div class="form-group">
                    <label>اسم الشهادة</label>
                    <input type="text" value="${cert.name}" onchange="cvBuilder.updateItem('certifications', ${index}, 'name', this.value)">
                </div>
                <div class="form-group">
                    <label>الجهة المانحة</label>
                    <input type="text" value="${cert.issuer}" onchange="cvBuilder.updateItem('certifications', ${index}, 'issuer', this.value)">
                </div>
                <div class="form-group">
                    <label>تاريخ الحصول</label>
                    <input type="month" value="${cert.date}" onchange="cvBuilder.updateItem('certifications', ${index}, 'date', this.value)">
                </div>
                <div class="form-group">
                    <label>رابط الشهادة</label>
                    <input type="url" value="${cert.url}" onchange="cvBuilder.updateItem('certifications', ${index}, 'url', this.value)">
                </div>
            </div>
            <div class="form-group">
                <label>الوصف</label>
                <textarea rows="2" placeholder="وصف مختصر للشهادة..." onchange="cvBuilder.updateItem('certifications', ${index}, 'description', this.value)">${cert.description}</textarea>
            </div>
        `;
        return item;
    }

    // Languages section methods
    populateLanguages() {
        const languagesList = document.getElementById('languages-list');
        if (!languagesList) return;

        languagesList.innerHTML = '';
        this.cvData.languages.forEach((lang, index) => {
            const langItem = this.createLanguageItem(lang, index);
            languagesList.appendChild(langItem);
        });
    }

    createLanguageItem(lang, index) {
        const item = document.createElement('div');
        item.className = 'language-item';
        item.innerHTML = `
            <button class="remove-item" onclick="cvBuilder.removeItem('languages', ${index})">
                <i class="fas fa-times"></i>
            </button>
            <div class="form-grid">
                <div class="form-group">
                    <label>اللغة</label>
                    <input type="text" value="${lang.name}" onchange="cvBuilder.updateItem('languages', ${index}, 'name', this.value)">
                </div>
                <div class="form-group">
                    <label>مستوى الإتقان</label>
                    <select onchange="cvBuilder.updateItem('languages', ${index}, 'proficiency', this.value)">
                        <option value="basic" ${lang.proficiency === 'basic' ? 'selected' : ''}>أساسي</option>
                        <option value="intermediate" ${lang.proficiency === 'intermediate' ? 'selected' : ''}>متوسط</option>
                        <option value="advanced" ${lang.proficiency === 'advanced' ? 'selected' : ''}>متقدم</option>
                        <option value="fluent" ${lang.proficiency === 'fluent' ? 'selected' : ''}>طلق</option>
                        <option value="native" ${lang.proficiency === 'native' ? 'selected' : ''}>لغة أم</option>
                    </select>
                </div>
            </div>
        `;
        return item;
    }

    updatePreview() {
        const previewContainer = document.getElementById('cv-preview');
        if (!previewContainer) return;

        // Generate HTML based on selected template
        const templateHTML = this.generateTemplateHTML();
        previewContainer.innerHTML = templateHTML;
    }

    generateTemplateHTML() {
        const { personal, summary, experience, education, skills } = this.cvData;
        
        return `
            <div class="cv-template ${this.cvData.template}">
                <div class="cv-header">
                    <h1 class="cv-name">${personal.fullName || 'Your Name'}</h1>
                    <h2 class="cv-title">${personal.jobTitle || 'Your Job Title'}</h2>
                    <div class="cv-contact">
                        ${personal.email ? `<span><i class="fas fa-envelope"></i> ${personal.email}</span>` : ''}
                        ${personal.phone ? `<span><i class="fas fa-phone"></i> ${personal.phone}</span>` : ''}
                        ${personal.location ? `<span><i class="fas fa-map-marker-alt"></i> ${personal.location}</span>` : ''}
                        ${personal.website ? `<span><i class="fas fa-globe"></i> ${personal.website}</span>` : ''}
                        ${personal.linkedin ? `<span><i class="fab fa-linkedin"></i> LinkedIn</span>` : ''}
                        ${personal.github ? `<span><i class="fab fa-github"></i> GitHub</span>` : ''}
                    </div>
                </div>

                ${summary ? `
                    <div class="cv-section">
                        <h3 class="cv-section-title">Professional Summary</h3>
                        <div class="cv-summary">${summary}</div>
                    </div>
                ` : ''}

                ${experience.length > 0 ? `
                    <div class="cv-section">
                        <h3 class="cv-section-title">Work Experience</h3>
                        ${experience.map(exp => `
                            <div class="cv-experience-item">
                                <div class="cv-item-header">
                                    <div>
                                        <div class="cv-item-title">${exp.title}</div>
                                        <div class="cv-item-company">${exp.company}</div>
                                    </div>
                                    <div class="cv-item-date">
                                        ${exp.startDate} - ${exp.current ? 'Present' : exp.endDate}
                                    </div>
                                </div>
                                ${exp.description ? `<div class="cv-item-description">${exp.description}</div>` : ''}
                            </div>
                        `).join('')}
                    </div>
                ` : ''}

                ${skills.length > 0 ? `
                    <div class="cv-section">
                        <h3 class="cv-section-title">Skills</h3>
                        <div class="cv-skill-list">
                            ${skills.map(skill => `<span class="cv-skill">${skill.name}</span>`).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
    }

    async handlePhotoUpload(file) {
        // Handle photo upload (placeholder)
        console.log('Photo upload:', file.name);
        this.showMessage('Photo upload feature coming soon!', 'info');
    }

    async saveCV() {
        try {
            this.updateCVData();
            
            if (this.currentCV) {
                // Update existing CV
                const { data, error } = await window.dbService.updateCV(this.currentCV.id, {
                    title: this.cvData.title,
                    cv_data: this.cvData
                });
                
                if (error) throw error;
                this.showMessage('CV saved successfully!', 'success');
            } else {
                // Create new CV
                const { data, error } = await window.dbService.createCV(
                    this.currentUser.id,
                    this.cvData.title,
                    null, // template_id
                    this.cvData
                );
                
                if (error) throw error;
                this.currentCV = data;
                this.showMessage('CV created successfully!', 'success');
            }
        } catch (error) {
            console.error('Error saving CV:', error);
            this.showMessage('Failed to save CV', 'error');
        }
    }

    async loadCV(cvId) {
        try {
            const { data, error } = await window.dbService.getCV(cvId);
            if (error) throw error;
            
            this.currentCV = data;
            this.cvData = data.cv_data;
            this.populateForm();
            this.updatePreview();
        } catch (error) {
            console.error('Error loading CV:', error);
            this.showMessage('Failed to load CV', 'error');
        }
    }

    async loadTemplate(templateId) {
        try {
            const { data, error } = await window.dbService.getTemplate(templateId);
            if (error) throw error;
            
            // Apply template data to CV
            this.cvData.template = data.name.toLowerCase().replace(/\s+/g, '-');
            this.populateForm();
            this.updatePreview();
        } catch (error) {
            console.error('Error loading template:', error);
            this.showMessage('Failed to load template', 'error');
        }
    }

    setupAutoSave() {
        this.autoSaveInterval = setInterval(() => {
            if (this.currentCV) {
                this.saveCV();
            }
        }, 30000); // Auto-save every 30 seconds
    }

    async exportToPDF() {
        try {
            const previewElement = document.getElementById('cv-preview');
            if (!previewElement) {
                this.showMessage('Please switch to preview tab first', 'warning');
                return;
            }

            this.showMessage('Generating PDF...', 'info');
            
            // Use html2canvas and jsPDF to generate PDF
            const canvas = await html2canvas(previewElement, {
                scale: 2,
                useCORS: true,
                allowTaint: true
            });

            const imgData = canvas.toDataURL('image/png');
            const pdf = new window.jspdf.jsPDF('p', 'mm', 'a4');
            
            const imgWidth = 210;
            const pageHeight = 295;
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            let heightLeft = imgHeight;
            let position = 0;

            pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
            heightLeft -= pageHeight;

            while (heightLeft >= 0) {
                position = heightLeft - imgHeight;
                pdf.addPage();
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
            }

            pdf.save(`${this.cvData.title || 'CV'}.pdf`);
            this.showMessage('PDF exported successfully!', 'success');
        } catch (error) {
            console.error('Error exporting PDF:', error);
            this.showMessage('Failed to export PDF', 'error');
        }
    }

    async getAISuggestions() {
        this.showMessage('AI suggestions feature coming soon!', 'info');
    }

    async generateSummary() {
        this.showMessage('AI summary generation coming soon!', 'info');
    }

    // Add new items methods
    addExperience() {
        const newExperience = {
            title: '',
            company: '',
            location: '',
            startDate: '',
            endDate: '',
            current: false,
            description: ''
        };

        this.cvData.experience.push(newExperience);
        this.populateExperience();
        this.updatePreview();
        this.saveToLocalStorage();
    }

    addEducation() {
        const newEducation = {
            degree: '',
            school: '',
            location: '',
            startDate: '',
            endDate: '',
            gpa: '',
            description: ''
        };

        this.cvData.education.push(newEducation);
        this.populateEducation();
        this.updatePreview();
        this.saveToLocalStorage();
    }

    addProject() {
        const newProject = {
            name: '',
            technologies: '',
            url: '',
            startDate: '',
            endDate: '',
            description: ''
        };

        this.cvData.projects.push(newProject);
        this.populateProjects();
        this.updatePreview();
        this.saveToLocalStorage();
    }

    addCertification() {
        const newCertification = {
            name: '',
            issuer: '',
            date: '',
            url: '',
            description: ''
        };

        this.cvData.certifications.push(newCertification);
        this.populateCertifications();
        this.updatePreview();
        this.saveToLocalStorage();
    }

    addLanguage() {
        const newLanguage = {
            name: '',
            proficiency: 'intermediate'
        };

        this.cvData.languages.push(newLanguage);
        this.populateLanguages();
        this.updatePreview();
        this.saveToLocalStorage();
    }

    // Remove item method
    removeItem(section, index) {
        if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
            this.cvData[section].splice(index, 1);

            // Re-populate the section
            switch(section) {
                case 'experience':
                    this.populateExperience();
                    break;
                case 'education':
                    this.populateEducation();
                    break;
                case 'projects':
                    this.populateProjects();
                    break;
                case 'certifications':
                    this.populateCertifications();
                    break;
                case 'languages':
                    this.populateLanguages();
                    break;
            }

            this.updatePreview();
            this.saveToLocalStorage();
        }
    }

    // Update item method
    updateItem(section, index, field, value) {
        if (this.cvData[section] && this.cvData[section][index]) {
            this.cvData[section][index][field] = value;
            this.updatePreview();
            this.saveToLocalStorage();
        }
    }

    // Utility methods
    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    showMessage(message, type = 'info') {
        if (window.authManager) {
            window.authManager.showMessage(message, type);
        }
    }

    // Cleanup
    destroy() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }
    }
}

// Initialize CV builder when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.cvBuilder = new CVBuilder();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.cvBuilder) {
        window.cvBuilder.destroy();
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CVBuilder;
}
