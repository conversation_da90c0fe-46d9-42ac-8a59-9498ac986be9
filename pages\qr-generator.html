<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد رمز QR - Elashrafy CV</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/arabic.css">
    <link rel="stylesheet" href="../css/responsive.css">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- QR Code Library -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    
    <style>
        .qr-main {
            padding-top: 80px;
            min-height: 100vh;
            background: var(--gray-50);
        }
        
        .qr-header {
            text-align: center;
            padding: var(--spacing-12) 0;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: var(--white);
            margin-bottom: var(--spacing-12);
        }
        
        .qr-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-8);
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .qr-form-section,
        .qr-preview-section {
            background: var(--white);
            border-radius: var(--radius-xl);
            padding: var(--spacing-8);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
        }
        
        .section-title {
            font-size: var(--font-size-2xl);
            font-weight: 600;
            color: var(--gray-900);
            margin-bottom: var(--spacing-6);
            padding-bottom: var(--spacing-3);
            border-bottom: 2px solid var(--gray-100);
        }
        
        .cv-selector {
            margin-bottom: var(--spacing-6);
        }
        
        .cv-list {
            display: grid;
            gap: var(--spacing-3);
        }
        
        .cv-option {
            display: flex;
            align-items: center;
            gap: var(--spacing-3);
            padding: var(--spacing-4);
            border: 2px solid var(--gray-200);
            border-radius: var(--radius-lg);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .cv-option:hover {
            border-color: var(--primary-color);
            background: var(--primary-color);
            background-opacity: 0.05;
        }
        
        .cv-option.selected {
            border-color: var(--primary-color);
            background: var(--primary-color);
            background-opacity: 0.1;
        }
        
        .cv-option input[type="radio"] {
            margin: 0;
        }
        
        .cv-info h4 {
            margin: 0 0 var(--spacing-1) 0;
            color: var(--gray-900);
        }
        
        .cv-info p {
            margin: 0;
            color: var(--gray-600);
            font-size: var(--font-size-sm);
        }
        
        .qr-settings {
            margin-bottom: var(--spacing-6);
        }
        
        .settings-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-4);
        }
        
        .qr-preview {
            text-align: center;
            margin-bottom: var(--spacing-6);
        }
        
        .qr-canvas {
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-4);
        }
        
        .qr-info {
            background: var(--gray-50);
            padding: var(--spacing-4);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-6);
        }
        
        .qr-actions {
            display: flex;
            gap: var(--spacing-3);
            flex-wrap: wrap;
        }
        
        .qr-actions .btn {
            flex: 1;
            justify-content: center;
        }
        
        .color-picker {
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
        }
        
        .color-input {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: var(--radius);
            cursor: pointer;
        }
        
        .saved-qr-codes {
            margin-top: var(--spacing-12);
        }
        
        .qr-codes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: var(--spacing-6);
        }
        
        .qr-code-card {
            background: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-4);
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            text-align: center;
        }
        
        .qr-code-preview {
            width: 100px;
            height: 100px;
            margin: 0 auto var(--spacing-3);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius);
        }
        
        @media (max-width: 768px) {
            .qr-container {
                grid-template-columns: 1fr;
                gap: var(--spacing-6);
            }
            
            .settings-grid {
                grid-template-columns: 1fr;
            }
            
            .qr-actions {
                flex-direction: column;
            }
            
            .qr-actions .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="../index.html">
                        <i class="fas fa-file-alt"></i>
                        <span>Elashrafy CV</span>
                    </a>
                </div>
                
                <nav class="nav">
                    <ul class="nav-list">
                        <li><a href="../index.html" class="nav-link">الرئيسية</a></li>
                        <li><a href="dashboard.html" class="nav-link">لوحة التحكم</a></li>
                        <li><a href="cv-builder.html" class="nav-link">منشئ السيرة الذاتية</a></li>
                        <li><a href="templates.html" class="nav-link">القوالب</a></li>
                        <li><a href="qr-generator.html" class="nav-link active">مولد QR</a></li>
                        <li><a href="nfc-manager.html" class="nav-link">إدارة NFC</a></li>
                    </ul>
                </nav>
                
                <div class="header-actions">
                    <div class="language-switcher">
                        <button class="language-btn" onclick="toggleLanguage()">
                            <i class="fas fa-globe"></i>
                            <span id="current-language">العربية</span>
                        </button>
                    </div>
                    
                    <div class="user-menu">
                        <button class="user-btn" id="user-menu-btn">
                            <i class="fas fa-user"></i>
                            <span id="user-name">المستخدم</span>
                        </button>
                        <div class="user-dropdown" id="user-dropdown">
                            <a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a>
                            <a href="#" onclick="logout()"><i class="fas fa-sign-out-alt"></i> تسجيل الخروج</a>
                        </div>
                    </div>
                    
                    <button class="nav-toggle" id="nav-toggle">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="qr-main">
        <!-- Header Section -->
        <section class="qr-header">
            <div class="container">
                <h1><i class="fas fa-qrcode"></i> مولد رمز QR</h1>
                <p>أنشئ رموز QR لسيرتك الذاتية لمشاركتها بسهولة</p>
            </div>
        </section>

        <div class="container">
            <!-- QR Generator -->
            <div class="qr-container">
                <!-- Form Section -->
                <div class="qr-form-section">
                    <h2 class="section-title">
                        <i class="fas fa-cog"></i>
                        إعدادات رمز QR
                    </h2>
                    
                    <!-- CV Selector -->
                    <div class="cv-selector">
                        <h3>اختر السيرة الذاتية</h3>
                        <div class="cv-list" id="cv-list">
                            <!-- CV options will be loaded here -->
                            <div class="loading-placeholder">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p>جاري تحميل السير الذاتية...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- QR Settings -->
                    <div class="qr-settings">
                        <h3>إعدادات التصميم</h3>
                        <div class="settings-grid">
                            <div class="form-group">
                                <label>حجم الرمز</label>
                                <select id="qr-size">
                                    <option value="200">صغير (200px)</option>
                                    <option value="300" selected>متوسط (300px)</option>
                                    <option value="400">كبير (400px)</option>
                                    <option value="500">كبير جداً (500px)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>مستوى تصحيح الأخطاء</label>
                                <select id="error-correction">
                                    <option value="L">منخفض (7%)</option>
                                    <option value="M" selected>متوسط (15%)</option>
                                    <option value="Q">عالي (25%)</option>
                                    <option value="H">عالي جداً (30%)</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label>لون المقدمة</label>
                                <div class="color-picker">
                                    <input type="color" id="foreground-color" value="#000000" class="color-input">
                                    <span>أسود</span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>لون الخلفية</label>
                                <div class="color-picker">
                                    <input type="color" id="background-color" value="#ffffff" class="color-input">
                                    <span>أبيض</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- QR Title -->
                    <div class="form-group">
                        <label>عنوان رمز QR</label>
                        <input type="text" id="qr-title" placeholder="مثال: سيرتي الذاتية - أحمد محمد">
                        <div class="form-help">
                            <i class="fas fa-info-circle"></i>
                            سيتم استخدام هذا العنوان لحفظ وإدارة رمز QR
                        </div>
                    </div>
                    
                    <button class="btn btn-primary btn-full" onclick="generateQR()">
                        <i class="fas fa-magic"></i>
                        إنشاء رمز QR
                    </button>
                </div>
                
                <!-- Preview Section -->
                <div class="qr-preview-section">
                    <h2 class="section-title">
                        <i class="fas fa-eye"></i>
                        معاينة رمز QR
                    </h2>
                    
                    <div class="qr-preview">
                        <canvas id="qr-canvas" class="qr-canvas"></canvas>
                        <p id="qr-status">اختر سيرة ذاتية لإنشاء رمز QR</p>
                    </div>
                    
                    <div class="qr-info" id="qr-info" style="display: none;">
                        <h4>معلومات رمز QR</h4>
                        <p><strong>الرابط:</strong> <span id="qr-url"></span></p>
                        <p><strong>الحجم:</strong> <span id="qr-size-info"></span></p>
                        <p><strong>تاريخ الإنشاء:</strong> <span id="qr-date"></span></p>
                    </div>
                    
                    <div class="qr-actions" id="qr-actions" style="display: none;">
                        <button class="btn btn-outline" onclick="downloadQR()">
                            <i class="fas fa-download"></i>
                            تحميل PNG
                        </button>
                        <button class="btn btn-outline" onclick="downloadQRSVG()">
                            <i class="fas fa-vector-square"></i>
                            تحميل SVG
                        </button>
                        <button class="btn btn-primary" onclick="saveQR()">
                            <i class="fas fa-save"></i>
                            حفظ رمز QR
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Saved QR Codes -->
            <section class="saved-qr-codes">
                <h2 class="section-title">
                    <i class="fas fa-archive"></i>
                    رموز QR المحفوظة
                </h2>
                <div class="qr-codes-grid" id="saved-qr-codes">
                    <!-- Saved QR codes will be loaded here -->
                    <div class="loading-placeholder">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>جاري تحميل رموز QR المحفوظة...</p>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="footer-logo">
                        <i class="fas fa-file-alt"></i>
                        <span>Elashrafy CV</span>
                    </div>
                    <p>منصة شاملة لإنشاء وإدارة السيرة الذاتية بتقنيات حديثة</p>
                </div>
                
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="../index.html">الرئيسية</a></li>
                        <li><a href="cv-builder.html">منشئ السيرة الذاتية</a></li>
                        <li><a href="templates.html">القوالب</a></li>
                        <li><a href="dashboard.html">لوحة التحكم</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>الدعم</h4>
                    <ul>
                        <li><a href="#help">المساعدة</a></li>
                        <li><a href="#contact">اتصل بنا</a></li>
                        <li><a href="#privacy">سياسة الخصوصية</a></li>
                        <li><a href="#terms">شروط الاستخدام</a></li>
                    </ul>
                </div>
                
                <div class="footer-section">
                    <h4>تابعنا</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <p>&copy; 2024 Elashrafy CV. جميع الحقوق محفوظة.</p>
                <p>تم التطوير بواسطة <a href="#" class="footer-credit">Elashrafy</a></p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script src="../js/supabase-config.js"></script>
    <script src="../js/auth.js"></script>
    <script src="../js/db-service.js"></script>
    <script src="../js/translations.js"></script>
    <script src="../js/main.js"></script>
    
    <script>
        // QR Generator functionality will be added here
        class QRGenerator {
            constructor() {
                this.selectedCV = null;
                this.currentQR = null;
                this.userCVs = [];
                this.savedQRCodes = [];
                this.init();
            }

            async init() {
                // Check authentication
                if (!window.authManager || !window.authManager.isAuthenticated()) {
                    window.location.href = '../index.html';
                    return;
                }

                await this.loadUserCVs();
                await this.loadSavedQRCodes();
                this.setupEventListeners();
            }

            async loadUserCVs() {
                try {
                    const user = window.authManager.getCurrentUser();
                    const { data, error } = await window.dbService.getUserCVs(user.id);
                    
                    if (error) throw error;
                    
                    this.userCVs = data || [];
                    this.renderCVList();
                } catch (error) {
                    console.error('Error loading CVs:', error);
                    this.showMessage('فشل في تحميل السير الذاتية', 'error');
                }
            }

            renderCVList() {
                const cvList = document.getElementById('cv-list');
                
                if (this.userCVs.length === 0) {
                    cvList.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-file-alt"></i>
                            <p>لا توجد سير ذاتية متاحة</p>
                            <a href="cv-builder.html" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                إنشاء سيرة ذاتية
                            </a>
                        </div>
                    `;
                    return;
                }

                cvList.innerHTML = this.userCVs.map(cv => `
                    <div class="cv-option" onclick="selectCV('${cv.id}')">
                        <input type="radio" name="selected-cv" value="${cv.id}">
                        <div class="cv-info">
                            <h4>${cv.title}</h4>
                            <p>آخر تحديث: ${new Date(cv.updated_at).toLocaleDateString('ar-SA')}</p>
                        </div>
                    </div>
                `).join('');
            }

            setupEventListeners() {
                // Settings change listeners
                ['qr-size', 'error-correction', 'foreground-color', 'background-color'].forEach(id => {
                    const element = document.getElementById(id);
                    if (element) {
                        element.addEventListener('change', () => {
                            if (this.selectedCV) {
                                this.generateQRCode();
                            }
                        });
                    }
                });
            }

            async generateQRCode() {
                if (!this.selectedCV) return;

                try {
                    const cv = this.userCVs.find(cv => cv.id === this.selectedCV);
                    if (!cv) return;

                    // Generate CV URL
                    const cvUrl = `${window.location.origin}/cv-view.html?id=${cv.id}`;

                    // Get settings
                    const size = parseInt(document.getElementById('qr-size').value);
                    const errorCorrectionLevel = document.getElementById('error-correction').value;
                    const foregroundColor = document.getElementById('foreground-color').value;
                    const backgroundColor = document.getElementById('background-color').value;

                    // Generate QR code
                    const canvas = document.getElementById('qr-canvas');
                    await QRCode.toCanvas(canvas, cvUrl, {
                        width: size,
                        margin: 2,
                        color: {
                            dark: foregroundColor,
                            light: backgroundColor
                        },
                        errorCorrectionLevel: errorCorrectionLevel
                    });

                    // Update UI
                    document.getElementById('qr-status').textContent = 'تم إنشاء رمز QR بنجاح!';
                    document.getElementById('qr-url').textContent = cvUrl;
                    document.getElementById('qr-size-info').textContent = `${size}x${size} بكسل`;
                    document.getElementById('qr-date').textContent = new Date().toLocaleDateString('ar-SA');

                    document.getElementById('qr-info').style.display = 'block';
                    document.getElementById('qr-actions').style.display = 'flex';

                    this.currentQR = {
                        cvId: this.selectedCV,
                        url: cvUrl,
                        settings: { size, errorCorrectionLevel, foregroundColor, backgroundColor }
                    };

                } catch (error) {
                    console.error('Error generating QR code:', error);
                    this.showMessage('فشل في إنشاء رمز QR', 'error');
                }
            }

            downloadQRCode(format = 'png') {
                if (!this.currentQR) {
                    this.showMessage('لا يوجد رمز QR للتحميل', 'warning');
                    return;
                }

                const canvas = document.getElementById('qr-canvas');
                const cv = this.userCVs.find(cv => cv.id === this.selectedCV);
                const filename = `QR_${cv?.title || 'CV'}_${new Date().toISOString().split('T')[0]}`;

                if (format === 'png') {
                    const link = document.createElement('a');
                    link.download = `${filename}.png`;
                    link.href = canvas.toDataURL();
                    link.click();
                } else if (format === 'svg') {
                    // For SVG, we need to regenerate using SVG format
                    QRCode.toString(this.currentQR.url, {
                        type: 'svg',
                        width: this.currentQR.settings.size,
                        color: {
                            dark: this.currentQR.settings.foregroundColor,
                            light: this.currentQR.settings.backgroundColor
                        }
                    }).then(svg => {
                        const blob = new Blob([svg], { type: 'image/svg+xml' });
                        const link = document.createElement('a');
                        link.download = `${filename}.svg`;
                        link.href = URL.createObjectURL(blob);
                        link.click();
                    });
                }

                this.showMessage('تم تحميل رمز QR بنجاح!', 'success');
            }

            async saveQRCode() {
                if (!this.currentQR) {
                    this.showMessage('لا يوجد رمز QR للحفظ', 'warning');
                    return;
                }

                const title = document.getElementById('qr-title').value.trim();
                if (!title) {
                    this.showMessage('يرجى إدخال عنوان لرمز QR', 'warning');
                    return;
                }

                try {
                    const user = window.authManager.getCurrentUser();
                    const canvas = document.getElementById('qr-canvas');
                    const qrImage = canvas.toDataURL();

                    const { error } = await window.dbService.createQRCode({
                        user_id: user.id,
                        cv_id: this.selectedCV,
                        title: title,
                        qr_url: this.currentQR.url,
                        qr_image: qrImage,
                        settings: this.currentQR.settings
                    });

                    if (error) throw error;

                    this.showMessage('تم حفظ رمز QR بنجاح!', 'success');
                    await this.loadSavedQRCodes();

                    // Clear title input
                    document.getElementById('qr-title').value = '';

                } catch (error) {
                    console.error('Error saving QR code:', error);
                    this.showMessage('فشل في حفظ رمز QR', 'error');
                }
            }

            async loadSavedQRCodes() {
                try {
                    const user = window.authManager.getCurrentUser();
                    const { data, error } = await window.dbService.getUserQRCodes(user.id);

                    if (error) throw error;

                    this.savedQRCodes = data || [];
                    this.renderSavedQRCodes();
                } catch (error) {
                    console.error('Error loading saved QR codes:', error);
                }
            }

            renderSavedQRCodes() {
                const container = document.getElementById('saved-qr-codes');

                if (this.savedQRCodes.length === 0) {
                    container.innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-qrcode"></i>
                            <p>لا توجد رموز QR محفوظة</p>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = this.savedQRCodes.map(qr => `
                    <div class="qr-code-card">
                        <div class="qr-code-preview">
                            <img src="${qr.qr_image}" alt="${qr.title}">
                        </div>
                        <h4>${qr.title}</h4>
                        <p>تاريخ الإنشاء: ${new Date(qr.created_at).toLocaleDateString('ar-SA')}</p>
                        <div class="qr-card-actions">
                            <button class="btn btn-sm btn-outline" onclick="downloadSavedQR('${qr.id}')">
                                <i class="fas fa-download"></i>
                                تحميل
                            </button>
                            <button class="btn btn-sm btn-outline" onclick="deleteSavedQR('${qr.id}')">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                `).join('');
            }

            async deleteSavedQR(qrId) {
                if (!confirm('هل أنت متأكد من حذف رمز QR هذا؟')) {
                    return;
                }

                try {
                    const { error } = await window.dbService.deleteQRCode(qrId);
                    if (error) throw error;

                    this.showMessage('تم حذف رمز QR بنجاح!', 'success');
                    await this.loadSavedQRCodes();
                } catch (error) {
                    console.error('Error deleting QR code:', error);
                    this.showMessage('فشل في حذف رمز QR', 'error');
                }
            }

            showMessage(message, type = 'info') {
                if (window.authManager) {
                    window.authManager.showMessage(message, type);
                }
            }
        }

        // Global functions
        function selectCV(cvId) {
            const options = document.querySelectorAll('.cv-option');
            options.forEach(opt => opt.classList.remove('selected'));
            
            const selectedOption = document.querySelector(`[onclick="selectCV('${cvId}')"]`);
            selectedOption.classList.add('selected');
            
            const radio = selectedOption.querySelector('input[type="radio"]');
            radio.checked = true;
            
            window.qrGenerator.selectedCV = cvId;
            window.qrGenerator.generateQRCode();
        }

        function generateQR() {
            if (window.qrGenerator.selectedCV) {
                window.qrGenerator.generateQRCode();
            } else {
                window.qrGenerator.showMessage('يرجى اختيار سيرة ذاتية أولاً', 'warning');
            }
        }

        function downloadQR() {
            window.qrGenerator.downloadQRCode('png');
        }

        function downloadQRSVG() {
            window.qrGenerator.downloadQRCode('svg');
        }

        function saveQR() {
            window.qrGenerator.saveQRCode();
        }

        function downloadSavedQR(qrId) {
            const qr = window.qrGenerator.savedQRCodes.find(q => q.id === qrId);
            if (qr) {
                const link = document.createElement('a');
                link.download = `${qr.title}.png`;
                link.href = qr.qr_image;
                link.click();
                window.qrGenerator.showMessage('تم تحميل رمز QR بنجاح!', 'success');
            }
        }

        function deleteSavedQR(qrId) {
            window.qrGenerator.deleteSavedQR(qrId);
        }

        // Initialize QR generator
        document.addEventListener('DOMContentLoaded', () => {
            window.qrGenerator = new QRGenerator();
        });
    </script>
</body>
</html>
